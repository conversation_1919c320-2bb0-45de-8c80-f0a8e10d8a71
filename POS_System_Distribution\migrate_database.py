#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ترقية قاعدة البيانات لتتوافق مع النظام الجديد
"""

import sqlite3
import os
from datetime import datetime

def migrate_database():
    """ترقية قاعدة البيانات"""
    db_file = "pos_system.db"
    
    if not os.path.exists(db_file):
        print(f"❌ ملف قاعدة البيانات {db_file} غير موجود")
        return False
    
    print("🔄 بدء ترقية قاعدة البيانات...")
    
    # إنشاء نسخة احتياطية
    backup_file = f"pos_system_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
    import shutil
    shutil.copy2(db_file, backup_file)
    print(f"✅ تم إنشاء نسخة احتياطية: {backup_file}")
    
    conn = sqlite3.connect(db_file)
    cursor = conn.cursor()
    
    try:
        # التحقق من هيكل الجدول الحالي
        cursor.execute("PRAGMA table_info(products)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]
        
        print(f"📋 الأعمدة الحالية: {column_names}")
        
        if 'category_id' not in column_names and 'category' in column_names:
            print("🔧 تحديث جدول products...")
            
            # إضافة عمود category_id
            cursor.execute("ALTER TABLE products ADD COLUMN category_id INTEGER")
            print("✅ تم إضافة عمود category_id")
            
            # تحديث البيانات الموجودة
            print("🔄 تحديث البيانات الموجودة...")
            
            # الحصول على الفئات
            cursor.execute("SELECT id, name FROM categories")
            categories = cursor.fetchall()
            category_map = {name: id for id, name in categories}
            
            print(f"📂 الفئات المتاحة: {category_map}")
            
            # تحديث المنتجات
            cursor.execute("SELECT id, category FROM products WHERE category IS NOT NULL")
            products = cursor.fetchall()
            
            updated_count = 0
            for product_id, category_name in products:
                if category_name in category_map:
                    category_id = category_map[category_name]
                    cursor.execute("UPDATE products SET category_id = ? WHERE id = ?", 
                                 (category_id, product_id))
                    updated_count += 1
                    print(f"   - تحديث منتج {product_id}: {category_name} -> {category_id}")
                else:
                    # إنشاء فئة جديدة للفئات غير الموجودة
                    cursor.execute("INSERT INTO categories (name, description, color) VALUES (?, ?, ?)",
                                 (category_name, f"فئة {category_name}", "#4CAF50"))
                    new_category_id = cursor.lastrowid
                    cursor.execute("UPDATE products SET category_id = ? WHERE id = ?", 
                                 (new_category_id, product_id))
                    updated_count += 1
                    print(f"   - إنشاء فئة جديدة: {category_name} (ID: {new_category_id})")
                    print(f"   - تحديث منتج {product_id}: {category_name} -> {new_category_id}")
            
            print(f"✅ تم تحديث {updated_count} منتج")
            
            # يمكن الآن حذف العمود القديم، لكن SQLite لا يدعم DROP COLUMN
            # لذلك سنتركه كما هو
            print("ℹ️  تم الاحتفاظ بالعمود القديم 'category' للتوافق")
            
        else:
            print("✅ قاعدة البيانات محدثة بالفعل")
        
        conn.commit()
        print("✅ تم حفظ التغييرات")
        
        # التحقق من النتيجة
        cursor.execute("SELECT COUNT(*) FROM products WHERE category_id IS NOT NULL")
        updated_products = cursor.fetchone()[0]
        print(f"📊 عدد المنتجات المحدثة: {updated_products}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الترقية: {e}")
        conn.rollback()
        return False
        
    finally:
        conn.close()

def test_migration():
    """اختبار الترقية"""
    print("\n🧪 اختبار الترقية...")
    
    from database import Database
    
    try:
        db = Database()
        
        # اختبار إضافة منتج
        categories = db.get_all_categories()
        if categories:
            category_id = categories[0][0]
            result = db.add_product("منتج اختبار الترقية", 10.0, 5, category_id, "MIGRATE123")
            
            if result is not None:
                print("✅ اختبار إضافة المنتج نجح")
                
                # حذف المنتج التجريبي
                products = db.get_all_products()
                for prod in products:
                    if prod[1] == "منتج اختبار الترقية":
                        # يمكن حذفه هنا إذا أردت
                        break
                        
                return True
            else:
                print("❌ اختبار إضافة المنتج فشل")
                return False
        else:
            print("❌ لا توجد فئات للاختبار")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def main():
    print("=" * 60)
    print("🔧 ترقية قاعدة البيانات")
    print("=" * 60)
    
    if migrate_database():
        print("\n" + "=" * 60)
        print("✅ تمت الترقية بنجاح!")
        
        if test_migration():
            print("✅ اختبار الترقية نجح!")
            print("\nيمكنك الآن استخدام النظام بشكل طبيعي:")
            print("python simple_pos.py")
        else:
            print("❌ اختبار الترقية فشل!")
            
    else:
        print("\n❌ فشلت الترقية!")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
