#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الإصلاحات الجديدة
"""

from database import Database

def test_add_category():
    """اختبار إضافة فئة"""
    print("🧪 اختبار إضافة فئة...")
    
    db = Database("test_fixes.db")
    
    # إضافة فئة جديدة
    result = db.add_category("فئة اختبار", "وصف الفئة", "#FF5722")
    
    if result is not None:
        print(f"✅ تم إضافة الفئة بنجاح - ID: {result}")
        
        # التحقق من الفئة
        categories = db.get_all_categories()
        found = False
        for cat in categories:
            if cat[1] == "فئة اختبار":
                found = True
                print(f"✅ تم العثور على الفئة: {cat}")
                break
        
        if not found:
            print("❌ لم يتم العثور على الفئة في قاعدة البيانات")
            return False
            
    else:
        print("❌ فشل في إضافة الفئة")
        return False
    
    return True

def test_add_product():
    """اختبار إضافة منتج"""
    print("\n🧪 اختبار إضافة منتج...")
    
    db = Database("test_fixes.db")
    
    # الحصول على أول فئة
    categories = db.get_all_categories()
    if not categories:
        print("❌ لا توجد فئات لإضافة المنتج")
        return False
    
    category_id = categories[0][0]
    
    # إضافة منتج جديد
    result = db.add_product("منتج اختبار", 25.50, 10, category_id, "TEST123")
    
    if result is not None:
        print(f"✅ تم إضافة المنتج بنجاح - ID: {result}")
        
        # التحقق من المنتج
        products = db.get_all_products()
        found = False
        for prod in products:
            if prod[1] == "منتج اختبار":
                found = True
                print(f"✅ تم العثور على المنتج: {prod[:6]}")  # أول 6 حقول فقط
                break
        
        if not found:
            print("❌ لم يتم العثور على المنتج في قاعدة البيانات")
            return False
            
    else:
        print("❌ فشل في إضافة المنتج")
        return False
    
    return True

def test_database_functions():
    """اختبار وظائف قاعدة البيانات"""
    print("=" * 50)
    print("🧪 اختبار الإصلاحات الجديدة")
    print("=" * 50)
    
    # حذف ملف الاختبار إذا كان موجوداً
    import os
    if os.path.exists("test_fixes.db"):
        os.remove("test_fixes.db")
    
    success = True
    
    # اختبار إضافة فئة
    if not test_add_category():
        success = False
    
    # اختبار إضافة منتج
    if not test_add_product():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✅ جميع الاختبارات نجحت!")
        print("الإصلاحات تعمل بشكل صحيح.")
    else:
        print("❌ بعض الاختبارات فشلت!")
    print("=" * 50)
    
    # تنظيف
    if os.path.exists("test_fixes.db"):
        os.remove("test_fixes.db")
    
    return success

if __name__ == "__main__":
    test_database_functions()
