#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام نقطة البيع
"""

import os
import sys

def test_imports():
    """اختبار استيراد المكتبات"""
    print("اختبار استيراد المكتبات...")
    
    try:
        import tkinter as tk
        print("✓ tkinter")
    except ImportError as e:
        print(f"✗ tkinter: {e}")
        return False
    
    try:
        import sqlite3
        print("✓ sqlite3")
    except ImportError as e:
        print(f"✗ sqlite3: {e}")
        return False
    
    try:
        from reportlab.lib.pagesizes import A4
        from reportlab.platypus import SimpleDocTemplate
        print("✓ reportlab")
    except ImportError as e:
        print(f"✗ reportlab: {e}")
        return False
    
    try:
        import qrcode
        print("✓ qrcode")
    except ImportError as e:
        print(f"✗ qrcode: {e}")
        return False
    
    try:
        from PIL import Image
        print("✓ Pillow")
    except ImportError as e:
        print(f"✗ Pillow: {e}")
        return False
    
    return True

def test_database():
    """اختبار قاعدة البيانات"""
    print("\nاختبار قاعدة البيانات...")
    
    try:
        from database import Database
        db = Database("test_pos.db")
        
        # اختبار إضافة منتج
        result = db.add_product("منتج تجريبي", 10.50, 100, "TEST123", "اختبار")
        if result:
            print("✓ إضافة منتج")
        else:
            print("✗ إضافة منتج")
            return False
        
        # اختبار البحث
        products = db.search_product("منتج تجريبي")
        if products:
            print("✓ البحث عن منتج")
        else:
            print("✗ البحث عن منتج")
            return False
        
        # اختبار إنشاء فاتورة
        items = [{
            'product_id': products[0][0],
            'product_name': products[0][1],
            'unit_price': products[0][2],
            'quantity': 2
        }]
        
        invoice_id, invoice_number = db.create_invoice("عميل تجريبي", "123456789", items)
        if invoice_id:
            print("✓ إنشاء فاتورة")
        else:
            print("✗ إنشاء فاتورة")
            return False
        
        # حذف ملف الاختبار
        if os.path.exists("test_pos.db"):
            os.remove("test_pos.db")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في قاعدة البيانات: {e}")
        return False

def test_invoice_generator():
    """اختبار مولد الفواتير"""
    print("\nاختبار مولد الفواتير...")
    
    try:
        from database import Database
        from invoice_generator import InvoiceGenerator
        
        # إنشاء قاعدة بيانات مؤقتة
        db = Database("test_invoice.db")
        generator = InvoiceGenerator(db)
        
        # إضافة منتج واختبار الفاتورة
        db.add_product("منتج للفاتورة", 25.00, 50, "INV123", "اختبار")
        products = db.search_product("منتج للفاتورة")
        
        items = [{
            'product_id': products[0][0],
            'product_name': products[0][1],
            'unit_price': products[0][2],
            'quantity': 3
        }]
        
        invoice_id, invoice_number = db.create_invoice("عميل الفاتورة", "987654321", items)
        
        # إنشاء PDF
        pdf_filename = generator.generate_invoice_pdf(invoice_id, "test_invoice.pdf")
        
        if os.path.exists("test_invoice.pdf"):
            print("✓ إنشاء فاتورة PDF")
            os.remove("test_invoice.pdf")  # حذف الملف التجريبي
        else:
            print("✗ إنشاء فاتورة PDF")
            return False
        
        # حذف ملف قاعدة البيانات التجريبية
        if os.path.exists("test_invoice.db"):
            os.remove("test_invoice.db")
        
        return True
        
    except Exception as e:
        print(f"✗ خطأ في مولد الفواتير: {e}")
        return False

def main():
    """تشغيل جميع الاختبارات"""
    print("=" * 50)
    print("اختبار نظام نقطة البيع")
    print("=" * 50)
    
    all_tests_passed = True
    
    # اختبار المكتبات
    if not test_imports():
        all_tests_passed = False
    
    # اختبار قاعدة البيانات
    if not test_database():
        all_tests_passed = False
    
    # اختبار مولد الفواتير
    if not test_invoice_generator():
        all_tests_passed = False
    
    print("\n" + "=" * 50)
    if all_tests_passed:
        print("✅ جميع الاختبارات نجحت! النظام جاهز للاستخدام.")
        print("\nلتشغيل النظام:")
        print("python main.py")
        print("أو")
        print("python run.py")
    else:
        print("❌ بعض الاختبارات فشلت. يرجى مراجعة الأخطاء أعلاه.")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
