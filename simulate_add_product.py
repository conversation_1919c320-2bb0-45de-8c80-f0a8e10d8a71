#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محاكاة عملية إضافة منتج
"""

from database import Database
import os

def simulate_add_product():
    """محاكاة عملية إضافة منتج كما تحدث في الواجهة"""
    print("🔄 محاكاة عملية إضافة منتج...")
    
    # استخدام قاعدة البيانات الحالية
    db = Database()
    
    print("\n1️⃣ الحصول على الفئات:")
    categories = db.get_all_categories()
    if not categories:
        print("❌ لا توجد فئات!")
        return False
    
    for i, cat in enumerate(categories):
        print(f"   {i+1}. {cat[1]} (ID: {cat[0]})")
    
    # اختيار أول فئة (محاكاة اختيار المستخدم)
    selected_category = categories[0][0]
    print(f"\n2️⃣ الفئة المختارة: {categories[0][1]} (ID: {selected_category})")
    
    # بيانات المنتج (محاكاة إدخال المستخدم)
    name = "منتج تجريبي جديد"
    price = 15.75
    quantity = 5
    barcode = "SIM123"
    
    print(f"\n3️⃣ بيانات المنتج:")
    print(f"   - الاسم: {name}")
    print(f"   - السعر: {price}")
    print(f"   - الكمية: {quantity}")
    print(f"   - الباركود: {barcode}")
    print(f"   - الفئة: {selected_category}")
    
    # التحقق من البيانات (محاكاة التحقق في الواجهة)
    print(f"\n4️⃣ التحقق من البيانات:")
    
    if not name.strip():
        print("❌ اسم المنتج فارغ")
        return False
    print("✅ اسم المنتج صحيح")
    
    if price <= 0:
        print("❌ السعر غير صحيح")
        return False
    print("✅ السعر صحيح")
    
    if quantity < 0:
        print("❌ الكمية غير صحيحة")
        return False
    print("✅ الكمية صحيحة")
    
    if not selected_category:
        print("❌ لم يتم اختيار فئة")
        return False
    print("✅ الفئة محددة")
    
    # محاولة الإضافة
    print(f"\n5️⃣ محاولة إضافة المنتج...")
    try:
        result = db.add_product(name, price, quantity, selected_category, barcode)
        print(f"نتيجة add_product: {result}")
        print(f"نوع النتيجة: {type(result)}")
        
        if result is not None:
            print("✅ تم إرجاع نتيجة صحيحة - المنتج تم إضافته")
            
            # التحقق من وجود المنتج
            print(f"\n6️⃣ التحقق من وجود المنتج:")
            products = db.get_all_products()
            found = False
            for prod in products:
                if prod[1] == name:
                    found = True
                    print(f"✅ تم العثور على المنتج: {prod[1]} (ID: {prod[0]})")
                    break
            
            if not found:
                print("❌ لم يتم العثور على المنتج في قاعدة البيانات!")
                return False
                
            return True
        else:
            print("❌ تم إرجاع None - فشل في الإضافة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في add_product: {e}")
        return False

def main():
    print("=" * 50)
    print("🧪 محاكاة إضافة منتج")
    print("=" * 50)
    
    success = simulate_add_product()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ المحاكاة نجحت! المشكلة ليست في قاعدة البيانات")
        print("المشكلة قد تكون في:")
        print("1. عدم اختيار فئة في الواجهة")
        print("2. خطأ في قراءة البيانات من الحقول")
        print("3. خطأ في استدعاء الوظيفة")
    else:
        print("❌ المحاكاة فشلت!")
    print("=" * 50)

if __name__ == "__main__":
    main()
