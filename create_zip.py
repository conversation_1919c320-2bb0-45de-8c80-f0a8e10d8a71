#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف ZIP للتوزيع
"""

import zipfile
import os
from datetime import datetime

def create_distribution_zip():
    """إنشاء ملف ZIP للتوزيع"""
    
    dist_folder = "POS_System_Distribution"
    
    if not os.path.exists(dist_folder):
        print("❌ مجلد التوزيع غير موجود")
        return False
    
    # اسم ملف ZIP
    timestamp = datetime.now().strftime("%Y%m%d_%H%M")
    zip_filename = f"نظام_نقطة_البيع_v1.0_{timestamp}.zip"
    
    print(f"📦 إنشاء ملف ZIP: {zip_filename}")
    
    try:
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # إضافة جميع الملفات في مجلد التوزيع
            for root, dirs, files in os.walk(dist_folder):
                for file in files:
                    file_path = os.path.join(root, file)
                    # الحصول على المسار النسبي
                    arcname = os.path.relpath(file_path, dist_folder)
                    zipf.write(file_path, arcname)
                    print(f"✅ تم إضافة: {arcname}")
        
        # التحقق من حجم الملف
        file_size = os.path.getsize(zip_filename)
        size_mb = file_size / (1024 * 1024)
        
        print(f"\n🎉 تم إنشاء ملف ZIP بنجاح!")
        print(f"📁 اسم الملف: {zip_filename}")
        print(f"📊 حجم الملف: {size_mb:.1f} MB")
        print(f"📍 المسار: {os.path.abspath(zip_filename)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف ZIP: {e}")
        return False

def main():
    print("=" * 50)
    print("📦 إنشاء ملف ZIP للتوزيع")
    print("=" * 50)
    
    if create_distribution_zip():
        print("\n" + "=" * 50)
        print("✅ تم إنشاء ملف التوزيع بنجاح!")
        print("🚀 يمكنك الآن مشاركة هذا الملف مع الآخرين")
        print("📋 المستخدم يحتاج فقط لاستخراج الملف وتشغيله")
        print("=" * 50)
    else:
        print("\n❌ فشل في إنشاء ملف التوزيع")

if __name__ == "__main__":
    main()
