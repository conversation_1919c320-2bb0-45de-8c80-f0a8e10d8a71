import sqlite3
from datetime import datetime
import os

class Database:
    def __init__(self, db_name="pos_system.db"):
        self.db_name = db_name
        self.init_database()
    
    def get_connection(self):
        return sqlite3.connect(self.db_name)
    
    def init_database(self):
        """إنشاء الجداول الأساسية"""
        conn = self.get_connection()
        cursor = conn.cursor()

        # جدول الفئات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                color TEXT DEFAULT '#4CAF50',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # جدول المنتجات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                price REAL NOT NULL,
                quantity INTEGER NOT NULL DEFAULT 0,
                barcode TEXT UNIQUE,
                category_id INTEGER,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (category_id) REFERENCES categories (id)
            )
        ''')

        # جدول الفواتير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                customer_name TEXT,
                customer_vat TEXT,
                total_amount REAL NOT NULL,
                vat_amount REAL NOT NULL DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # جدول عناصر الفاتورة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoice_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER,
                product_id INTEGER,
                product_name TEXT,
                quantity INTEGER NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                FOREIGN KEY (invoice_id) REFERENCES invoices (id),
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')

        # جدول إعدادات الشركة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS company_settings (
                id INTEGER PRIMARY KEY,
                company_name TEXT NOT NULL,
                vat_number TEXT NOT NULL,
                address TEXT,
                phone TEXT,
                email TEXT
            )
        ''')

        conn.commit()
        conn.close()

        # إضافة بيانات افتراضية
        self.add_default_data()
    
    def add_default_data(self):
        """إضافة بيانات افتراضية للنظام"""
        conn = self.get_connection()
        cursor = conn.cursor()

        # التحقق من وجود إعدادات الشركة
        cursor.execute("SELECT COUNT(*) FROM company_settings")
        if cursor.fetchone()[0] == 0:
            cursor.execute('''
                INSERT INTO company_settings (company_name, vat_number, address, phone, email)
                VALUES (?, ?, ?, ?, ?)
            ''', ("شركة المثال التجارية", "123456789012345", "الرياض، المملكة العربية السعودية",
                  "+966501234567", "<EMAIL>"))

        # إضافة فئات افتراضية
        cursor.execute("SELECT COUNT(*) FROM categories")
        if cursor.fetchone()[0] == 0:
            default_categories = [
                ("مشروبات", "مشروبات باردة وساخنة", "#2196F3"),
                ("وجبات خفيفة", "سناكس ومقرمشات", "#FF9800"),
                ("حلويات", "شوكولاتة وحلويات", "#E91E63"),
                ("مواد غذائية", "مواد أساسية ومعلبات", "#4CAF50"),
                ("قرطاسية", "أدوات مكتبية ومدرسية", "#9C27B0"),
                ("منظفات", "مواد تنظيف ومطهرات", "#00BCD4")
            ]

            cursor.executemany('''
                INSERT INTO categories (name, description, color)
                VALUES (?, ?, ?)
            ''', default_categories)

        # إضافة منتجات تجريبية
        cursor.execute("SELECT COUNT(*) FROM products")
        if cursor.fetchone()[0] == 0:
            # الحصول على معرفات الفئات
            cursor.execute("SELECT id, name FROM categories")
            categories = {name: id for id, name in cursor.fetchall()}

            sample_products = [
                ("كوكا كولا", 3.50, 100, "1001", categories.get("مشروبات", 1)),
                ("عصير برتقال", 4.00, 80, "1002", categories.get("مشروبات", 1)),
                ("شيبس ليز", 5.50, 60, "2001", categories.get("وجبات خفيفة", 2)),
                ("بسكويت أوريو", 8.00, 40, "2002", categories.get("وجبات خفيفة", 2)),
                ("شوكولاتة كيت كات", 6.50, 50, "3001", categories.get("حلويات", 3)),
                ("أرز بسمتي", 25.00, 30, "4001", categories.get("مواد غذائية", 4)),
                ("قلم أزرق", 2.50, 200, "5001", categories.get("قرطاسية", 5)),
                ("صابون سائل", 12.00, 25, "6001", categories.get("منظفات", 6))
            ]

            cursor.executemany('''
                INSERT INTO products (name, price, quantity, barcode, category_id)
                VALUES (?, ?, ?, ?, ?)
            ''', sample_products)

        conn.commit()
        conn.close()

    def add_category(self, name, description=None, color="#4CAF50"):
        """إضافة فئة جديدة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT INTO categories (name, description, color)
                VALUES (?, ?, ?)
            ''', (name, description, color))
            conn.commit()
            return cursor.lastrowid
        except sqlite3.IntegrityError:
            return None
        finally:
            conn.close()

    def get_all_categories(self):
        """الحصول على جميع الفئات"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM categories ORDER BY name")
        categories = cursor.fetchall()
        conn.close()
        return categories

    def get_category_by_id(self, category_id):
        """الحصول على فئة بالمعرف"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM categories WHERE id = ?", (category_id,))
        category = cursor.fetchone()
        conn.close()
        return category

    def delete_category(self, category_id):
        """حذف فئة (مع التحقق من وجود منتجات)"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # التحقق من وجود منتجات في هذه الفئة
            cursor.execute("SELECT COUNT(*) FROM products WHERE category_id = ?", (category_id,))
            product_count = cursor.fetchone()[0]

            if product_count > 0:
                return False, f"لا يمكن حذف الفئة لأنها تحتوي على {product_count} منتج"

            # حذف الفئة
            cursor.execute("DELETE FROM categories WHERE id = ?", (category_id,))

            if cursor.rowcount > 0:
                conn.commit()
                return True, "تم حذف الفئة بنجاح"
            else:
                return False, "الفئة غير موجودة"

        except Exception as e:
            return False, f"خطأ في حذف الفئة: {str(e)}"
        finally:
            conn.close()

    def delete_product(self, product_id):
        """حذف منتج"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # التحقق من عدم استخدام المنتج في فواتير
            cursor.execute("SELECT COUNT(*) FROM invoice_items WHERE product_id = ?", (product_id,))
            invoice_count = cursor.fetchone()[0]

            if invoice_count > 0:
                return False, f"لا يمكن حذف المنتج لأنه مستخدم في {invoice_count} فاتورة"

            # حذف المنتج
            cursor.execute("DELETE FROM products WHERE id = ?", (product_id,))

            if cursor.rowcount > 0:
                conn.commit()
                return True, "تم حذف المنتج بنجاح"
            else:
                return False, "المنتج غير موجود"

        except Exception as e:
            return False, f"خطأ في حذف المنتج: {str(e)}"
        finally:
            conn.close()

    def update_product(self, product_id, name, price, quantity, barcode=None):
        """تحديث بيانات منتج"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                UPDATE products
                SET name = ?, price = ?, quantity = ?, barcode = ?
                WHERE id = ?
            ''', (name, price, quantity, barcode, product_id))

            if cursor.rowcount > 0:
                conn.commit()
                return True, "تم تحديث المنتج بنجاح"
            else:
                return False, "المنتج غير موجود"

        except sqlite3.IntegrityError:
            return False, "الباركود مكرر"
        except Exception as e:
            return False, f"خطأ في تحديث المنتج: {str(e)}"
        finally:
            conn.close()

    def add_product(self, name, price, quantity, category_id, barcode=None):
        """إضافة منتج جديد"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT INTO products (name, price, quantity, barcode, category_id)
                VALUES (?, ?, ?, ?, ?)
            ''', (name, price, quantity, barcode, category_id))
            conn.commit()
            return cursor.lastrowid
        except sqlite3.IntegrityError:
            return None
        finally:
            conn.close()
    
    def get_all_products(self):
        """الحصول على جميع المنتجات مع أسماء الفئات"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT p.*, c.name as category_name, c.color as category_color
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            ORDER BY c.name, p.name
        ''')
        products = cursor.fetchall()
        conn.close()
        return products

    def get_products_by_category(self, category_id):
        """الحصول على المنتجات حسب الفئة"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT p.*, c.name as category_name, c.color as category_color
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.category_id = ?
            ORDER BY p.name
        ''', (category_id,))
        products = cursor.fetchall()
        conn.close()
        return products
    
    def search_product(self, search_term):
        """البحث عن منتج بالاسم أو الباركود"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT p.*, c.name as category_name, c.color as category_color
            FROM products p
            LEFT JOIN categories c ON p.category_id = c.id
            WHERE p.name LIKE ? OR p.barcode = ?
            ORDER BY p.name
        ''', (f"%{search_term}%", search_term))

        products = cursor.fetchall()
        conn.close()
        return products
    
    def update_product_quantity(self, product_id, new_quantity):
        """تحديث كمية المنتج"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE products SET quantity = ? WHERE id = ?
        ''', (new_quantity, product_id))
        
        conn.commit()
        conn.close()
    
    def create_invoice(self, customer_name, customer_vat, items):
        """إنشاء فاتورة جديدة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # إنشاء رقم فاتورة
        invoice_number = f"INV-{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        # حساب المجموع
        subtotal = sum(item['quantity'] * item['unit_price'] for item in items)
        vat_amount = subtotal * 0.15  # ضريبة القيمة المضافة 15%
        total_amount = subtotal + vat_amount
        
        # إدراج الفاتورة
        cursor.execute('''
            INSERT INTO invoices (invoice_number, customer_name, customer_vat, total_amount, vat_amount)
            VALUES (?, ?, ?, ?, ?)
        ''', (invoice_number, customer_name, customer_vat, total_amount, vat_amount))
        
        invoice_id = cursor.lastrowid
        
        # إدراج عناصر الفاتورة
        for item in items:
            cursor.execute('''
                INSERT INTO invoice_items (invoice_id, product_id, product_name, quantity, unit_price, total_price)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (invoice_id, item['product_id'], item['product_name'], 
                  item['quantity'], item['unit_price'], item['quantity'] * item['unit_price']))
            
            # تحديث المخزون
            cursor.execute('''
                UPDATE products SET quantity = quantity - ? WHERE id = ?
            ''', (item['quantity'], item['product_id']))
        
        conn.commit()
        conn.close()
        
        return invoice_id, invoice_number
    
    def get_invoice(self, invoice_id):
        """الحصول على تفاصيل الفاتورة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # بيانات الفاتورة
        cursor.execute("SELECT * FROM invoices WHERE id = ?", (invoice_id,))
        invoice = cursor.fetchone()
        
        # عناصر الفاتورة
        cursor.execute("SELECT * FROM invoice_items WHERE invoice_id = ?", (invoice_id,))
        items = cursor.fetchall()
        
        conn.close()
        return invoice, items
    
    def get_company_settings(self):
        """الحصول على إعدادات الشركة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM company_settings LIMIT 1")
        settings = cursor.fetchone()
        conn.close()
        return settings
