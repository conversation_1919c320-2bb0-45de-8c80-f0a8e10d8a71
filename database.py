import sqlite3
from datetime import datetime
import os

class Database:
    def __init__(self, db_name="pos_system.db"):
        self.db_name = db_name
        self.init_database()
    
    def get_connection(self):
        return sqlite3.connect(self.db_name)
    
    def init_database(self):
        """إنشاء الجداول الأساسية"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # جدول المنتجات
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                price REAL NOT NULL,
                quantity INTEGER NOT NULL DEFAULT 0,
                barcode TEXT UNIQUE,
                category TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول الفواتير
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                customer_name TEXT,
                customer_vat TEXT,
                total_amount REAL NOT NULL,
                vat_amount REAL NOT NULL DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # جدول عناصر الفاتورة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS invoice_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER,
                product_id INTEGER,
                product_name TEXT,
                quantity INTEGER NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                FOREIGN KEY (invoice_id) REFERENCES invoices (id),
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        ''')
        
        # جدول إعدادات الشركة
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS company_settings (
                id INTEGER PRIMARY KEY,
                company_name TEXT NOT NULL,
                vat_number TEXT NOT NULL,
                address TEXT,
                phone TEXT,
                email TEXT
            )
        ''')
        
        conn.commit()
        conn.close()
        
        # إضافة بيانات افتراضية
        self.add_default_data()
    
    def add_default_data(self):
        """إضافة بيانات افتراضية للنظام"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # التحقق من وجود إعدادات الشركة
        cursor.execute("SELECT COUNT(*) FROM company_settings")
        if cursor.fetchone()[0] == 0:
            cursor.execute('''
                INSERT INTO company_settings (company_name, vat_number, address, phone, email)
                VALUES (?, ?, ?, ?, ?)
            ''', ("شركة المثال التجارية", "123456789012345", "الرياض، المملكة العربية السعودية", 
                  "+966501234567", "<EMAIL>"))
        
        # إضافة منتجات تجريبية
        cursor.execute("SELECT COUNT(*) FROM products")
        if cursor.fetchone()[0] == 0:
            sample_products = [
                ("قلم أزرق", 2.50, 100, "1234567890123", "قرطاسية"),
                ("دفتر A4", 15.00, 50, "1234567890124", "قرطاسية"),
                ("آلة حاسبة", 45.00, 25, "1234567890125", "إلكترونيات"),
                ("كوب قهوة", 8.50, 75, "1234567890126", "مطبخ"),
                ("قميص قطني", 85.00, 30, "1234567890127", "ملابس")
            ]
            
            cursor.executemany('''
                INSERT INTO products (name, price, quantity, barcode, category)
                VALUES (?, ?, ?, ?, ?)
            ''', sample_products)
        
        conn.commit()
        conn.close()
    
    def add_product(self, name, price, quantity, barcode=None, category=None):
        """إضافة منتج جديد"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO products (name, price, quantity, barcode, category)
                VALUES (?, ?, ?, ?, ?)
            ''', (name, price, quantity, barcode, category))
            conn.commit()
            return True
        except sqlite3.IntegrityError:
            return False
        finally:
            conn.close()
    
    def get_all_products(self):
        """الحصول على جميع المنتجات"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM products ORDER BY name")
        products = cursor.fetchall()
        conn.close()
        return products
    
    def search_product(self, search_term):
        """البحث عن منتج بالاسم أو الباركود"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT * FROM products 
            WHERE name LIKE ? OR barcode = ?
        ''', (f"%{search_term}%", search_term))
        
        products = cursor.fetchall()
        conn.close()
        return products
    
    def update_product_quantity(self, product_id, new_quantity):
        """تحديث كمية المنتج"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            UPDATE products SET quantity = ? WHERE id = ?
        ''', (new_quantity, product_id))
        
        conn.commit()
        conn.close()
    
    def create_invoice(self, customer_name, customer_vat, items):
        """إنشاء فاتورة جديدة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # إنشاء رقم فاتورة
        invoice_number = f"INV-{datetime.now().strftime('%Y%m%d%H%M%S')}"
        
        # حساب المجموع
        subtotal = sum(item['quantity'] * item['unit_price'] for item in items)
        vat_amount = subtotal * 0.15  # ضريبة القيمة المضافة 15%
        total_amount = subtotal + vat_amount
        
        # إدراج الفاتورة
        cursor.execute('''
            INSERT INTO invoices (invoice_number, customer_name, customer_vat, total_amount, vat_amount)
            VALUES (?, ?, ?, ?, ?)
        ''', (invoice_number, customer_name, customer_vat, total_amount, vat_amount))
        
        invoice_id = cursor.lastrowid
        
        # إدراج عناصر الفاتورة
        for item in items:
            cursor.execute('''
                INSERT INTO invoice_items (invoice_id, product_id, product_name, quantity, unit_price, total_price)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (invoice_id, item['product_id'], item['product_name'], 
                  item['quantity'], item['unit_price'], item['quantity'] * item['unit_price']))
            
            # تحديث المخزون
            cursor.execute('''
                UPDATE products SET quantity = quantity - ? WHERE id = ?
            ''', (item['quantity'], item['product_id']))
        
        conn.commit()
        conn.close()
        
        return invoice_id, invoice_number
    
    def get_invoice(self, invoice_id):
        """الحصول على تفاصيل الفاتورة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # بيانات الفاتورة
        cursor.execute("SELECT * FROM invoices WHERE id = ?", (invoice_id,))
        invoice = cursor.fetchone()
        
        # عناصر الفاتورة
        cursor.execute("SELECT * FROM invoice_items WHERE invoice_id = ?", (invoice_id,))
        items = cursor.fetchall()
        
        conn.close()
        return invoice, items
    
    def get_company_settings(self):
        """الحصول على إعدادات الشركة"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM company_settings LIMIT 1")
        settings = cursor.fetchone()
        conn.close()
        return settings
