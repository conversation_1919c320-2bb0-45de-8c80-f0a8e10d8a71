# 🛒 نظام نقطة البيع البسيط والجميل
## Simple and Beautiful POS System

نظام نقطة بيع محسن بواجهة أبسط وأجمل، مطور بلغة Python مع دعم كامل للغة العربية ومعايير هيئة الزكاة السعودية.

![POS System](https://img.shields.io/badge/Python-3.6+-blue.svg)
![License](https://img.shields.io/badge/License-Open%20Source-green.svg)
![ZATCA](https://img.shields.io/badge/ZATCA-Compliant-gold.svg)

## ✨ المميزات الجديدة

### 🎨 واجهة محسنة
- **تصميم أنيق وبسيط** - واجهة ثلاثية الأعمدة سهلة الاستخدام
- **ألوان جذابة** - نظام ألوان متناسق ومريح للعين
- **أيقونات تعبيرية** - استخدام الإيموجي لتحسين تجربة المستخدم
- **تأثيرات تفاعلية** - أزرار بتأثيرات hover جميلة

### 📂 إدارة الفئات المحسنة
- **إضافة فئات بألوان مخصصة** - 6 ألوان جاهزة للاختيار
- **عرض منتجات حسب الفئة** - تنظيم أفضل للمنتجات
- **🗑️ حذف الفئات الآمن** - مع التحقق من وجود منتجات
- **❌ أزرار حذف سريع** - بجانب كل فئة للحذف المباشر
- **فئات افتراضية جاهزة** - مشروبات، وجبات خفيفة، حلويات، وأكثر

### 🛍️ إدارة المنتجات المتقدمة
- **إضافة سهلة للمنتجات** - اختر الفئة أولاً ثم أضف المنتج
- **✏️ تعديل المنتجات** - تعديل الاسم، السعر، الكمية، والباركود
- **🗑️ حذف المنتجات الآمن** - مع التحقق من استخدامها في الفواتير
- **عرض شبكي جميل** - المنتجات معروضة في بطاقات أنيقة مع أزرار العمليات
- **بحث سريع** - البحث في جميع المنتجات فورياً
- **إدارة المخزون** - تتبع الكميات المتوفرة

### 🧾 فواتير احترافية
- **تصميم محسن** - فواتير أجمل مع ألوان وأيقونات
- **QR Code متطور** - حسب معايير ZATCA الجديدة
- **طباعة تلقائية** - فتح الفاتورة تلقائياً بعد الإنشاء

## 🚀 التشغيل السريع

```bash
# تشغيل النظام الجديد (مع تثبيت المتطلبات تلقائياً)
python run_simple.py
```

## 📋 كيفية الاستخدام

### 1️⃣ إضافة الفئات
1. انقر على **"➕ إضافة فئة"** في العمود الأيسر
2. أدخل اسم الفئة ووصفها
3. اختر لوناً مناسباً من الألوان المتاحة
4. انقر **"💾 حفظ"**

### 2️⃣ إدارة المنتجات
**إضافة منتج:**
1. **اختر فئة** من القائمة اليسرى أولاً
2. انقر **"➕ إضافة منتج"** في العمود الأوسط
3. أدخل اسم المنتج والسعر والكمية
4. أضف الباركود (اختياري)
5. انقر **"💾 حفظ المنتج"**

**تعديل منتج:**
1. انقر **"✏️"** على بطاقة المنتج للتعديل السريع
2. أو انقر **"✏️ تعديل منتج"** لاختيار من قائمة
3. عدّل البيانات المطلوبة
4. انقر **"💾 حفظ التغييرات"**

**حذف منتج:**
1. انقر **"🗑️"** على بطاقة المنتج للحذف السريع
2. أو انقر **"🗑️ حذف منتج"** لاختيار من قائمة
3. تأكيد الحذف (لا يمكن حذف منتج مستخدم في فواتير)

### 3️⃣ حذف الفئات
**حذف فئة:**
1. انقر **"❌"** بجانب الفئة للحذف السريع
2. أو انقر **"🗑️ حذف فئة"** لاختيار من قائمة
3. تأكيد الحذف (لا يمكن حذف فئة تحتوي على منتجات)

### 4️⃣ البيع والفواتير
1. **اختر فئة** لعرض منتجاتها
2. **انقر "➕"** على المنتجات المطلوبة
3. **راجع السلة** في العمود الأيمن
4. **أدخل اسم العميل** (اختياري)
5. **انقر "💳 إنشاء فاتورة"**

## 🎨 الألوان المتاحة للفئات

| اللون | الكود | الاستخدام المقترح |
|--------|-------|------------------|
| 🟢 أخضر | `#4CAF50` | مواد غذائية |
| 🔵 أزرق | `#2196F3` | مشروبات |
| 🟠 برتقالي | `#FF9800` | وجبات خفيفة |
| 🌸 وردي | `#E91E63` | حلويات |
| 🟣 بنفسجي | `#9C27B0` | قرطاسية |
| 🔷 سماوي | `#00BCD4` | منظفات |

## 📊 البيانات الافتراضية

النظام يأتي مع بيانات تجريبية جاهزة:

### الفئات الافتراضية:
- 🥤 **مشروبات** - مشروبات باردة وساخنة
- 🍿 **وجبات خفيفة** - سناكس ومقرمشات  
- 🍫 **حلويات** - شوكولاتة وحلويات
- 🥫 **مواد غذائية** - مواد أساسية ومعلبات
- ✏️ **قرطاسية** - أدوات مكتبية ومدرسية
- 🧽 **منظفات** - مواد تنظيف ومطهرات

### المنتجات التجريبية:
- كوكا كولا، عصير برتقال
- شيبس ليز، بسكويت أوريو
- شوكولاتة كيت كات
- أرز بسمتي، قلم أزرق، صابون سائل

## 🔧 المتطلبات التقنية

- **Python 3.6+** - مطلوب
- **tkinter** - مدمج مع Python
- **sqlite3** - مدمج مع Python  
- **reportlab** - لإنتاج PDF
- **qrcode[pil]** - لإنشاء QR Code
- **Pillow** - لمعالجة الصور

## 📁 هيكل الملفات

```
📦 نظام نقطة البيع البسيط
├── 🚀 run_simple.py          # ملف التشغيل الرئيسي
├── 🖥️ simple_pos.py          # الواجهة المحسنة
├── 🗄️ database.py            # قاعدة البيانات المحسنة
├── 🧾 invoice_generator.py   # مولد الفواتير المحسن
├── 📋 README_Simple.md       # هذا الملف
├── 🗃️ pos_system.db          # قاعدة البيانات (تُنشأ تلقائياً)
└── 📄 الفواتير/             # مجلد الفواتير المُنتجة
```

## 🆚 مقارنة مع النسخة السابقة

| الميزة | النسخة السابقة | النسخة الجديدة |
|--------|----------------|-----------------|
| الواجهة | جداول تقليدية | تصميم ثلاثي الأعمدة |
| الفئات | نص بسيط | أزرار ملونة تفاعلية |
| المنتجات | قائمة عادية | بطاقات أنيقة |
| الألوان | رمادي | نظام ألوان متناسق |
| الأيقونات | بدون | إيموجي تعبيرية |
| إضافة المنتجات | نافذة معقدة | خطوات مبسطة |

## 🛠️ حل المشاكل

### ❌ النظام لا يعمل
```bash
# تأكد من إصدار Python
python --version

# تشغيل الاختبار
python test_system.py
```

### ❌ مشاكل في المكتبات
```bash
# إعادة تثبيت المتطلبات
pip install --upgrade reportlab qrcode[pil] Pillow
```

### ❌ مشاكل في قاعدة البيانات
- احذف ملف `pos_system.db` وأعد تشغيل النظام
- سيتم إنشاء قاعدة بيانات جديدة تلقائياً

## 🎯 نصائح للاستخدام الأمثل

1. **ابدأ بالفئات** - أضف الفئات أولاً قبل المنتجات
2. **استخدم الألوان** - اختر ألواناً مناسبة لكل فئة
3. **نظم المنتجات** - ضع كل منتج في الفئة المناسبة
4. **احفظ نسخ احتياطية** - انسخ ملف `pos_system.db` دورياً
5. **استخدم البحث** - للعثور على المنتجات بسرعة

## 📞 الدعم والمساعدة

- 📧 للاستفسارات التقنية: راجع ملف `test_system.py`
- 🐛 للإبلاغ عن مشاكل: تحقق من رسائل الخطأ
- 💡 للاقتراحات: يمكن تطوير النظام حسب الحاجة

## 📜 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير بحرية.

---

**🎉 استمتع باستخدام نظام نقطة البيع البسيط والجميل!**

*تم التطوير بـ ❤️ باستخدام Python*
