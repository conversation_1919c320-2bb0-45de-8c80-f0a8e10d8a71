#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام نقطة البيع - POS System
تطبيق بسيط لإدارة المبيعات مع إنتاج فواتير تحتوي على QR Code حسب معايير هيئة الزكاة السعودية
"""

import sys
import subprocess
import os

def install_requirements():
    """تثبيت المتطلبات المطلوبة"""
    requirements = [
        'reportlab',
        'qrcode[pil]',
        'Pillow'
    ]
    
    print("جاري تثبيت المتطلبات...")
    for requirement in requirements:
        try:
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', requirement])
            print(f"✓ تم تثبيت {requirement}")
        except subprocess.CalledProcessError:
            print(f"✗ فشل في تثبيت {requirement}")
            return False
    
    print("تم تثبيت جميع المتطلبات بنجاح!")
    return True

def main():
    """تشغيل النظام"""
    print("=" * 50)
    print("نظام نقطة البيع - POS System")
    print("=" * 50)
    
    # التحقق من وجود المتطلبات
    try:
        import reportlab
        import qrcode
        from PIL import Image
        print("✓ جميع المتطلبات متوفرة")
    except ImportError as e:
        print(f"✗ مكتبة مفقودة: {e}")
        print("جاري تثبيت المتطلبات...")
        if not install_requirements():
            print("فشل في تثبيت المتطلبات. يرجى تثبيتها يدوياً:")
            print("pip install reportlab qrcode[pil] Pillow")
            return
    
    # تشغيل التطبيق
    try:
        from main import main as run_pos
        print("جاري تشغيل النظام...")
        run_pos()
    except Exception as e:
        print(f"خطأ في تشغيل النظام: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
