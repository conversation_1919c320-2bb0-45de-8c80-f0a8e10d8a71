import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from database import Database
from invoice_generator import InvoiceGenerator
import os
from datetime import datetime

class POSSystem:
    def __init__(self, root):
        self.root = root
        self.root.title("نظام نقطة البيع - POS System")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')
        
        # إنشاء قاعدة البيانات
        self.db = Database()
        self.invoice_generator = InvoiceGenerator(self.db)
        
        # سلة التسوق
        self.cart = []
        
        # إنشاء الواجهة
        self.create_widgets()
        self.load_products()
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # الإطار العلوي - البحث والمنتجات
        top_frame = ttk.LabelFrame(main_frame, text="المنتجات والبحث", padding=10)
        top_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # شريط البحث
        search_frame = ttk.Frame(top_frame)
        search_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(search_frame, text="البحث:").pack(side=tk.LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        self.search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=30)
        self.search_entry.pack(side=tk.LEFT, padx=(0, 5))
        self.search_entry.bind('<KeyRelease>', self.search_products)
        
        ttk.Button(search_frame, text="بحث", command=self.search_products).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Button(search_frame, text="إضافة منتج", command=self.add_product_dialog).pack(side=tk.RIGHT)
        
        # جدول المنتجات
        products_frame = ttk.Frame(top_frame)
        products_frame.pack(fill=tk.BOTH, expand=True)
        
        # إنشاء Treeview للمنتجات
        self.products_tree = ttk.Treeview(products_frame, columns=('ID', 'Name', 'Price', 'Quantity', 'Barcode', 'Category'), show='headings', height=10)
        
        # تعريف الأعمدة
        self.products_tree.heading('ID', text='الرقم')
        self.products_tree.heading('Name', text='اسم المنتج')
        self.products_tree.heading('Price', text='السعر')
        self.products_tree.heading('Quantity', text='الكمية')
        self.products_tree.heading('Barcode', text='الباركود')
        self.products_tree.heading('Category', text='الفئة')
        
        # تحديد عرض الأعمدة
        self.products_tree.column('ID', width=50)
        self.products_tree.column('Name', width=200)
        self.products_tree.column('Price', width=80)
        self.products_tree.column('Quantity', width=80)
        self.products_tree.column('Barcode', width=120)
        self.products_tree.column('Category', width=100)
        
        # شريط التمرير للمنتجات
        products_scrollbar = ttk.Scrollbar(products_frame, orient=tk.VERTICAL, command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=products_scrollbar.set)
        
        self.products_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        products_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # ربط النقر المزدوج لإضافة المنتج للسلة
        self.products_tree.bind('<Double-1>', self.add_to_cart)
        
        # الإطار السفلي - السلة والفاتورة
        bottom_frame = ttk.LabelFrame(main_frame, text="سلة التسوق والفاتورة", padding=10)
        bottom_frame.pack(fill=tk.BOTH, expand=True)
        
        # تقسيم الإطار السفلي
        left_bottom = ttk.Frame(bottom_frame)
        left_bottom.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        right_bottom = ttk.Frame(bottom_frame)
        right_bottom.pack(side=tk.RIGHT, fill=tk.Y)
        
        # جدول السلة
        ttk.Label(left_bottom, text="سلة التسوق:", font=('Arial', 12, 'bold')).pack(anchor=tk.W)
        
        cart_frame = ttk.Frame(left_bottom)
        cart_frame.pack(fill=tk.BOTH, expand=True, pady=(5, 0))
        
        self.cart_tree = ttk.Treeview(cart_frame, columns=('Name', 'Quantity', 'Price', 'Total'), show='headings', height=8)
        
        self.cart_tree.heading('Name', text='المنتج')
        self.cart_tree.heading('Quantity', text='الكمية')
        self.cart_tree.heading('Price', text='السعر')
        self.cart_tree.heading('Total', text='المجموع')
        
        self.cart_tree.column('Name', width=200)
        self.cart_tree.column('Quantity', width=80)
        self.cart_tree.column('Price', width=80)
        self.cart_tree.column('Total', width=100)
        
        cart_scrollbar = ttk.Scrollbar(cart_frame, orient=tk.VERTICAL, command=self.cart_tree.yview)
        self.cart_tree.configure(yscrollcommand=cart_scrollbar.set)
        
        self.cart_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        cart_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # أزرار السلة
        cart_buttons = ttk.Frame(left_bottom)
        cart_buttons.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(cart_buttons, text="حذف من السلة", command=self.remove_from_cart).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(cart_buttons, text="مسح السلة", command=self.clear_cart).pack(side=tk.LEFT, padx=(0, 5))
        
        # معلومات الفاتورة
        invoice_info = ttk.LabelFrame(right_bottom, text="معلومات الفاتورة", padding=10)
        invoice_info.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(invoice_info, text="اسم العميل:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.customer_name = tk.StringVar()
        ttk.Entry(invoice_info, textvariable=self.customer_name, width=20).grid(row=0, column=1, pady=2)
        
        ttk.Label(invoice_info, text="الرقم الضريبي:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.customer_vat = tk.StringVar()
        ttk.Entry(invoice_info, textvariable=self.customer_vat, width=20).grid(row=1, column=1, pady=2)
        
        # ملخص الفاتورة
        summary_frame = ttk.LabelFrame(right_bottom, text="ملخص الفاتورة", padding=10)
        summary_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.subtotal_label = ttk.Label(summary_frame, text="المجموع الفرعي: 0.00 ريال", font=('Arial', 10))
        self.subtotal_label.pack(anchor=tk.W)
        
        self.vat_label = ttk.Label(summary_frame, text="ضريبة القيمة المضافة: 0.00 ريال", font=('Arial', 10))
        self.vat_label.pack(anchor=tk.W)
        
        self.total_label = ttk.Label(summary_frame, text="المجموع الإجمالي: 0.00 ريال", font=('Arial', 12, 'bold'))
        self.total_label.pack(anchor=tk.W)
        
        # أزرار الفاتورة
        invoice_buttons = ttk.Frame(right_bottom)
        invoice_buttons.pack(fill=tk.X)
        
        ttk.Button(invoice_buttons, text="إنشاء فاتورة", command=self.create_invoice).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(invoice_buttons, text="طباعة آخر فاتورة", command=self.print_last_invoice).pack(fill=tk.X)
    
    def load_products(self):
        """تحميل المنتجات في الجدول"""
        # مسح البيانات الحالية
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)
        
        # تحميل المنتجات من قاعدة البيانات
        products = self.db.get_all_products()
        for product in products:
            self.products_tree.insert('', tk.END, values=product)
    
    def search_products(self, event=None):
        """البحث عن المنتجات"""
        search_term = self.search_var.get()
        
        # مسح البيانات الحالية
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)
        
        if search_term:
            products = self.db.search_product(search_term)
        else:
            products = self.db.get_all_products()
        
        for product in products:
            self.products_tree.insert('', tk.END, values=product)
    
    def add_to_cart(self, event=None):
        """إضافة منتج للسلة"""
        selection = self.products_tree.selection()
        if not selection:
            return
        
        item = self.products_tree.item(selection[0])
        product_data = item['values']
        
        # التحقق من توفر المنتج
        if product_data[3] <= 0:  # الكمية
            messagebox.showwarning("تحذير", "هذا المنتج غير متوفر في المخزون")
            return
        
        # البحث عن المنتج في السلة
        for cart_item in self.cart:
            if cart_item['product_id'] == product_data[0]:
                if cart_item['quantity'] < product_data[3]:  # التحقق من الكمية المتاحة
                    cart_item['quantity'] += 1
                    self.update_cart_display()
                    return
                else:
                    messagebox.showwarning("تحذير", "لا يمكن إضافة كمية أكثر من المتوفر")
                    return
        
        # إضافة منتج جديد للسلة
        cart_item = {
            'product_id': product_data[0],
            'product_name': product_data[1],
            'unit_price': product_data[2],
            'quantity': 1
        }
        self.cart.append(cart_item)
        self.update_cart_display()
    
    def update_cart_display(self):
        """تحديث عرض السلة"""
        # مسح البيانات الحالية
        for item in self.cart_tree.get_children():
            self.cart_tree.delete(item)
        
        subtotal = 0
        for cart_item in self.cart:
            total_price = cart_item['quantity'] * cart_item['unit_price']
            subtotal += total_price
            
            self.cart_tree.insert('', tk.END, values=(
                cart_item['product_name'],
                cart_item['quantity'],
                f"{cart_item['unit_price']:.2f}",
                f"{total_price:.2f}"
            ))
        
        # تحديث ملخص الفاتورة
        vat_amount = subtotal * 0.15
        total_amount = subtotal + vat_amount
        
        self.subtotal_label.config(text=f"المجموع الفرعي: {subtotal:.2f} ريال")
        self.vat_label.config(text=f"ضريبة القيمة المضافة: {vat_amount:.2f} ريال")
        self.total_label.config(text=f"المجموع الإجمالي: {total_amount:.2f} ريال")
    
    def remove_from_cart(self):
        """حذف منتج من السلة"""
        selection = self.cart_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج لحذفه")
            return
        
        item_index = self.cart_tree.index(selection[0])
        del self.cart[item_index]
        self.update_cart_display()
    
    def clear_cart(self):
        """مسح السلة"""
        if messagebox.askyesno("تأكيد", "هل تريد مسح جميع المنتجات من السلة؟"):
            self.cart.clear()
            self.update_cart_display()
    
    def create_invoice(self):
        """إنشاء فاتورة جديدة"""
        if not self.cart:
            messagebox.showwarning("تحذير", "السلة فارغة")
            return
        
        try:
            # إنشاء الفاتورة في قاعدة البيانات
            invoice_id, invoice_number = self.db.create_invoice(
                self.customer_name.get() or "عميل نقدي",
                self.customer_vat.get(),
                self.cart
            )
            
            # إنشاء ملف PDF
            pdf_filename = self.invoice_generator.generate_invoice_pdf(invoice_id)
            
            messagebox.showinfo("نجح", f"تم إنشاء الفاتورة بنجاح\nرقم الفاتورة: {invoice_number}\nتم حفظ الملف: {pdf_filename}")
            
            # مسح السلة وإعادة تحميل المنتجات
            self.cart.clear()
            self.update_cart_display()
            self.customer_name.set("")
            self.customer_vat.set("")
            self.load_products()
            
            # حفظ رقم آخر فاتورة
            self.last_invoice_id = invoice_id
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء الفاتورة: {str(e)}")
    
    def print_last_invoice(self):
        """طباعة آخر فاتورة"""
        if hasattr(self, 'last_invoice_id'):
            try:
                pdf_filename = self.invoice_generator.generate_invoice_pdf(self.last_invoice_id)
                os.startfile(pdf_filename)  # فتح الملف بالبرنامج الافتراضي
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء طباعة الفاتورة: {str(e)}")
        else:
            messagebox.showwarning("تحذير", "لا توجد فاتورة للطباعة")

    def add_product_dialog(self):
        """نافذة إضافة منتج جديد"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة منتج جديد")
        dialog.geometry("400x300")
        dialog.resizable(False, False)

        # جعل النافذة في المقدمة
        dialog.transient(self.root)
        dialog.grab_set()

        # الحقول
        ttk.Label(dialog, text="اسم المنتج:").grid(row=0, column=0, sticky=tk.W, padx=10, pady=5)
        name_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=name_var, width=30).grid(row=0, column=1, padx=10, pady=5)

        ttk.Label(dialog, text="السعر:").grid(row=1, column=0, sticky=tk.W, padx=10, pady=5)
        price_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=price_var, width=30).grid(row=1, column=1, padx=10, pady=5)

        ttk.Label(dialog, text="الكمية:").grid(row=2, column=0, sticky=tk.W, padx=10, pady=5)
        quantity_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=quantity_var, width=30).grid(row=2, column=1, padx=10, pady=5)

        ttk.Label(dialog, text="الباركود:").grid(row=3, column=0, sticky=tk.W, padx=10, pady=5)
        barcode_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=barcode_var, width=30).grid(row=3, column=1, padx=10, pady=5)

        ttk.Label(dialog, text="الفئة:").grid(row=4, column=0, sticky=tk.W, padx=10, pady=5)
        category_var = tk.StringVar()
        ttk.Entry(dialog, textvariable=category_var, width=30).grid(row=4, column=1, padx=10, pady=5)

        def save_product():
            try:
                name = name_var.get().strip()
                price = float(price_var.get())
                quantity = int(quantity_var.get())
                barcode = barcode_var.get().strip() or None
                category = category_var.get().strip() or None

                if not name:
                    messagebox.showwarning("تحذير", "يرجى إدخال اسم المنتج")
                    return

                if self.db.add_product(name, price, quantity, barcode, category):
                    messagebox.showinfo("نجح", "تم إضافة المنتج بنجاح")
                    self.load_products()
                    dialog.destroy()
                else:
                    messagebox.showerror("خطأ", "فشل في إضافة المنتج (ربما الباركود مكرر)")

            except ValueError:
                messagebox.showerror("خطأ", "يرجى التأكد من صحة البيانات المدخلة")

        # الأزرار
        buttons_frame = ttk.Frame(dialog)
        buttons_frame.grid(row=5, column=0, columnspan=2, pady=20)

        ttk.Button(buttons_frame, text="حفظ", command=save_product).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="إلغاء", command=dialog.destroy).pack(side=tk.LEFT, padx=5)

def main():
    root = tk.Tk()
    app = POSSystem(root)
    root.mainloop()

if __name__ == "__main__":
    main()
