#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف تنفيذي للبرنامج
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def install_pyinstaller():
    """تثبيت PyInstaller"""
    print("📦 تثبيت PyInstaller...")
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', 'pyinstaller'])
        print("✅ تم تثبيت PyInstaller بنجاح")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت PyInstaller")
        return False

def check_required_files():
    """التحقق من وجود الملفات المطلوبة"""
    required_files = [
        'simple_pos.py',
        'database.py', 
        'invoice_generator.py',
        'migrate_database.py'
    ]
    
    print("🔍 التحقق من الملفات المطلوبة...")
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ {file} - مفقود")
            missing_files.append(file)
    
    return len(missing_files) == 0, missing_files

def create_spec_file():
    """إنشاء ملف .spec مخصص"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['simple_pos.py'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'sqlite3',
        'reportlab',
        'reportlab.lib',
        'reportlab.lib.pagesizes',
        'reportlab.lib.colors',
        'reportlab.lib.styles',
        'reportlab.lib.units',
        'reportlab.platypus',
        'reportlab.pdfbase',
        'reportlab.pdfbase.ttfonts',
        'qrcode',
        'qrcode.constants',
        'PIL',
        'PIL.Image',
        'datetime',
        'os',
        'base64',
        'json',
        'io'
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='نظام_نقطة_البيع',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)
'''
    
    with open('pos_system.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ تم إنشاء ملف pos_system.spec")

def build_executable():
    """بناء الملف التنفيذي"""
    print("🔨 بناء الملف التنفيذي...")
    
    try:
        # إنشاء ملف .spec مخصص
        create_spec_file()
        
        # بناء الملف التنفيذي
        cmd = [sys.executable, '-m', 'PyInstaller', '--clean', 'pos_system.spec']
        
        print("⏳ جاري البناء... (قد يستغرق عدة دقائق)")
        result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8')
        
        if result.returncode == 0:
            print("✅ تم بناء الملف التنفيذي بنجاح!")
            return True
        else:
            print("❌ فشل في بناء الملف التنفيذي")
            print("خطأ:", result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في البناء: {e}")
        return False

def create_distribution_folder():
    """إنشاء مجلد التوزيع"""
    print("📁 إنشاء مجلد التوزيع...")
    
    dist_folder = "POS_System_Distribution"
    
    # إنشاء المجلد
    if os.path.exists(dist_folder):
        shutil.rmtree(dist_folder)
    os.makedirs(dist_folder)
    
    # نسخ الملف التنفيذي
    exe_path = os.path.join("dist", "نظام_نقطة_البيع.exe")
    if os.path.exists(exe_path):
        shutil.copy2(exe_path, dist_folder)
        print(f"✅ تم نسخ الملف التنفيذي إلى {dist_folder}")
    else:
        print("❌ لم يتم العثور على الملف التنفيذي")
        return False
    
    # نسخ الملفات المهمة
    important_files = [
        'README_Simple.md',
        'دليل_النظام_الجديد.txt',
        'الحل_النهائي.txt',
        'migrate_database.py'
    ]
    
    for file in important_files:
        if os.path.exists(file):
            shutil.copy2(file, dist_folder)
            print(f"✅ تم نسخ {file}")
    
    # إنشاء ملف تعليمات
    create_instructions_file(dist_folder)
    
    return True

def create_instructions_file(dist_folder):
    """إنشاء ملف تعليمات الاستخدام"""
    instructions = """🛒 نظام نقطة البيع - تعليمات التشغيل
=====================================

🚀 كيفية التشغيل:
-----------------
1. انقر نقراً مزدوجاً على ملف "نظام_نقطة_البيع.exe"
2. انتظر قليلاً حتى يتم تحميل البرنامج
3. ستظهر واجهة النظام

📋 أول مرة تشغيل:
------------------
إذا كانت هذه أول مرة تستخدم فيها النظام:
1. سيتم إنشاء قاعدة البيانات تلقائياً
2. ستجد بيانات تجريبية جاهزة للاستخدام
3. يمكنك البدء فوراً في إضافة الفئات والمنتجات

🔧 في حالة وجود قاعدة بيانات قديمة:
------------------------------------
إذا كان لديك نسخة قديمة من النظام:
1. انسخ ملف "pos_system.db" من النسخة القديمة
2. ضعه في نفس مجلد الملف التنفيذي
3. شغّل البرنامج
4. إذا ظهرت رسالة خطأ، شغّل "migrate_database.py" أولاً

📁 الملفات المهمة:
------------------
- نظام_نقطة_البيع.exe: الملف التنفيذي الرئيسي
- pos_system.db: قاعدة البيانات (تُنشأ تلقائياً)
- migrate_database.py: أداة ترقية قاعدة البيانات
- الفواتير: تُحفظ كملفات PDF في نفس المجلد

🎯 الميزات:
-----------
✅ إضافة وحذف الفئات بألوان مختلفة
✅ إضافة وتعديل وحذف المنتجات
✅ إنشاء فواتير احترافية مع QR Code
✅ إدارة المخزون والكميات
✅ بحث سريع في المنتجات
✅ واجهة عربية جميلة وسهلة

🆘 في حالة المشاكل:
-------------------
- تأكد من أن Windows Defender لا يحجب البرنامج
- شغّل البرنامج كمدير إذا لزم الأمر
- تأكد من وجود مساحة كافية على القرص الصلب

📞 الدعم:
---------
راجع الملفات المرفقة للمزيد من التفاصيل:
- README_Simple.md: دليل شامل
- دليل_النظام_الجديد.txt: تعليمات مفصلة
- الحل_النهائي.txt: حلول المشاكل الشائعة

=====================================
🎉 استمتع باستخدام النظام!
====================================="""

    with open(os.path.join(dist_folder, "تعليمات_التشغيل.txt"), 'w', encoding='utf-8') as f:
        f.write(instructions)
    
    print("✅ تم إنشاء ملف تعليمات التشغيل")

def cleanup():
    """تنظيف الملفات المؤقتة"""
    print("🧹 تنظيف الملفات المؤقتة...")
    
    folders_to_remove = ['build', 'dist', '__pycache__']
    files_to_remove = ['pos_system.spec']
    
    for folder in folders_to_remove:
        if os.path.exists(folder):
            shutil.rmtree(folder)
            print(f"✅ تم حذف مجلد {folder}")
    
    for file in files_to_remove:
        if os.path.exists(file):
            os.remove(file)
            print(f"✅ تم حذف ملف {file}")

def main():
    print("=" * 60)
    print("🏗️  إنشاء ملف تنفيذي لنظام نقطة البيع")
    print("=" * 60)
    
    # التحقق من الملفات المطلوبة
    files_ok, missing = check_required_files()
    if not files_ok:
        print(f"❌ ملفات مفقودة: {missing}")
        return
    
    # تثبيت PyInstaller
    if not install_pyinstaller():
        return
    
    # بناء الملف التنفيذي
    if not build_executable():
        return
    
    # إنشاء مجلد التوزيع
    if not create_distribution_folder():
        return
    
    # تنظيف الملفات المؤقتة
    cleanup()
    
    print("\n" + "=" * 60)
    print("🎉 تم إنشاء الملف التنفيذي بنجاح!")
    print("📁 ستجد النظام في مجلد: POS_System_Distribution")
    print("🚀 يمكنك نسخ هذا المجلد لأي جهاز ويندوز وتشغيله")
    print("=" * 60)

if __name__ == "__main__":
    main()
