from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import qrcode
import io
import base64
from datetime import datetime
import json

class InvoiceGenerator:
    def __init__(self, database):
        self.database = database
        self.setup_fonts()
    
    def setup_fonts(self):
        """إعداد الخطوط العربية"""
        try:
            # يمكن إضافة خط عربي هنا إذا كان متوفراً
            pass
        except:
            pass
    
    def generate_zatca_qr_code(self, invoice_data):
        """إنشاء QR Code حسب معايير هيئة الزكاة السعودية"""
        company_settings = self.database.get_company_settings()
        
        # البيانات المطلوبة للـ QR Code حسب معايير ZATCA
        qr_data = {
            "seller_name": company_settings[1],  # اسم البائع
            "vat_number": company_settings[2],   # الرقم الضريبي
            "timestamp": invoice_data['created_at'],  # وقت الفاتورة
            "total_amount": str(invoice_data['total_amount']),  # المبلغ الإجمالي
            "vat_amount": str(invoice_data['vat_amount'])  # مبلغ الضريبة
        }
        
        # تحويل البيانات إلى تنسيق Base64 كما هو مطلوب
        qr_string = self.create_zatca_qr_string(qr_data)
        
        # إنشاء QR Code
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(qr_string)
        qr.make(fit=True)
        
        # تحويل إلى صورة
        qr_img = qr.make_image(fill_color="black", back_color="white")
        
        # حفظ في buffer
        img_buffer = io.BytesIO()
        qr_img.save(img_buffer, format='PNG')
        img_buffer.seek(0)
        
        return img_buffer
    
    def create_zatca_qr_string(self, qr_data):
        """إنشاء نص QR Code حسب معايير ZATCA"""
        # تنسيق البيانات حسب المعايير السعودية
        fields = [
            ("01", qr_data["seller_name"]),      # اسم البائع
            ("02", qr_data["vat_number"]),       # الرقم الضريبي
            ("03", qr_data["timestamp"]),        # الوقت
            ("04", qr_data["total_amount"]),     # المبلغ الإجمالي
            ("05", qr_data["vat_amount"])        # مبلغ الضريبة
        ]
        
        qr_string = ""
        for tag, value in fields:
            value_bytes = value.encode('utf-8')
            length = len(value_bytes)
            qr_string += tag + chr(length) + value
        
        return base64.b64encode(qr_string.encode('utf-8')).decode('utf-8')
    
    def generate_invoice_pdf(self, invoice_id, filename=None):
        """إنشاء فاتورة PDF"""
        invoice, items = self.database.get_invoice(invoice_id)
        company_settings = self.database.get_company_settings()
        
        if not filename:
            filename = f"invoice_{invoice[1]}.pdf"
        
        # إنشاء المستند
        doc = SimpleDocTemplate(filename, pagesize=A4)
        story = []
        styles = getSampleStyleSheet()
        
        # إعداد الأنماط
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1  # وسط
        )
        
        header_style = ParagraphStyle(
            'CustomHeader',
            parent=styles['Normal'],
            fontSize=12,
            spaceAfter=12
        )
        
        # عنوان الفاتورة مع تصميم أجمل
        title_text = """
        <para align="center">
        <font size="20" color="#2E7D32"><b>🧾 فاتورة ضريبية</b></font><br/>
        <font size="14" color="#666">Tax Invoice</font>
        </para>
        """
        story.append(Paragraph(title_text, styles['Normal']))
        story.append(Spacer(1, 30))

        # معلومات الشركة في إطار جميل
        company_info = f"""
        <para align="center" backColor="#f0f8ff" borderColor="#2196F3" borderWidth="2" borderPadding="15">
        <font size="14" color="#2E7D32"><b>🏢 {company_settings[1]}</b></font><br/>
        <font size="10" color="#666">الرقم الضريبي: {company_settings[2]}</font><br/>
        <font size="10" color="#666">📍 {company_settings[3]}</font><br/>
        <font size="10" color="#666">📞 {company_settings[4]}</font><br/>
        <font size="10" color="#666">📧 {company_settings[5]}</font>
        </para>
        """
        story.append(Paragraph(company_info, styles['Normal']))
        story.append(Spacer(1, 25))
        
        # معلومات الفاتورة بتصميم محسن
        invoice_info_data = [
            ["🧾 رقم الفاتورة", invoice[1]],
            ["📅 التاريخ", invoice[6][:19] if len(invoice[6]) > 19 else invoice[6]],
            ["👤 العميل", invoice[2] or "عميل نقدي"],
            ["🏢 الرقم الضريبي", invoice[3] or "غير محدد"]
        ]

        invoice_info_table = Table(invoice_info_data, colWidths=[2.5*inch, 3.5*inch])
        invoice_info_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.lightblue),
            ('TEXTCOLOR', (0, 0), (-1, -1), colors.black),
            ('ALIGN', (0, 0), (0, -1), 'RIGHT'),
            ('ALIGN', (1, 0), (1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 11),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 15),
            ('TOPPADDING', (0, 0), (-1, -1), 15),
            ('GRID', (0, 0), (-1, -1), 2, colors.darkblue),
            ('ROUNDEDCORNERS', (0, 0), (-1, -1), 5)
        ]))

        story.append(invoice_info_table)
        story.append(Spacer(1, 25))
        
        # جدول المنتجات بتصميم أجمل
        items_data = [["🛍️ المنتج", "📦 الكمية", "💰 السعر", "💵 المجموع"]]

        subtotal = 0
        for item in items:
            items_data.append([
                item[3],  # اسم المنتج
                str(item[4]),  # الكمية
                f"{item[5]:.2f} ريال",  # السعر
                f"{item[6]:.2f} ريال"   # المجموع
            ])
            subtotal += item[6]

        items_table = Table(items_data, colWidths=[3*inch, 1*inch, 1.5*inch, 1.5*inch])
        items_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkgreen),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('TOPPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.lightgreen),
            ('GRID', (0, 0), (-1, -1), 1, colors.darkgreen),
            ('ALTERNATEROWCOLOR', (0, 1), (-1, -1), colors.white)
        ]))

        story.append(items_table)
        story.append(Spacer(1, 25))
        
        # جدول المجاميع بتصميم محسن
        vat_amount = invoice[5]
        total_amount = invoice[4]

        totals_data = [
            ["💰 المجموع الفرعي", f"{subtotal:.2f} ريال"],
            ["🧾 ضريبة القيمة المضافة (15%)", f"{vat_amount:.2f} ريال"],
            ["💵 المجموع الإجمالي", f"{total_amount:.2f} ريال"]
        ]

        totals_table = Table(totals_data, colWidths=[3.5*inch, 2*inch])
        totals_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'RIGHT'),
            ('FONTNAME', (0, 0), (-1, -2), 'Helvetica-Bold'),
            ('FONTNAME', (0, -1), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -2), 11),
            ('FONTSIZE', (0, -1), (-1, -1), 14),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 15),
            ('TOPPADDING', (0, 0), (-1, -1), 15),
            ('BACKGROUND', (0, 0), (-1, -2), colors.lightyellow),
            ('BACKGROUND', (0, -1), (-1, -1), colors.lightgreen),
            ('TEXTCOLOR', (0, -1), (-1, -1), colors.darkgreen),
            ('GRID', (0, 0), (-1, -1), 2, colors.darkgreen)
        ]))

        story.append(totals_table)
        story.append(Spacer(1, 35))
        
        # إضافة QR Code
        invoice_data = {
            'created_at': invoice[6],
            'total_amount': total_amount,
            'vat_amount': vat_amount
        }
        
        qr_buffer = self.generate_zatca_qr_code(invoice_data)
        qr_image = Image(qr_buffer, width=2*inch, height=2*inch)
        
        # جدول QR Code مع تصميم أجمل
        qr_text = """
        <para align="center">
        <font size="12" color="#2E7D32"><b>📱 QR Code للتحقق</b></font><br/>
        <font size="10" color="#666">امسح الكود للتحقق من صحة الفاتورة</font><br/>
        <font size="9" color="#666">حسب معايير هيئة الزكاة والضريبة والجمارك</font><br/>
        <font size="8" color="#999">ZATCA Compliant QR Code</font>
        </para>
        """

        qr_data = [
            [qr_image, Paragraph(qr_text, styles['Normal'])]
        ]

        qr_table = Table(qr_data, colWidths=[2.5*inch, 3.5*inch])
        qr_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (0, 0), 'CENTER'),
            ('ALIGN', (1, 0), (1, 0), 'CENTER'),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
            ('BACKGROUND', (0, 0), (-1, -1), colors.lightcyan),
            ('GRID', (0, 0), (-1, -1), 2, colors.darkcyan),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 15),
            ('TOPPADDING', (0, 0), (-1, -1), 15)
        ]))

        story.append(qr_table)

        # إضافة تذييل جميل
        footer_text = """
        <para align="center">
        <font size="10" color="#2E7D32"><b>شكراً لتعاملكم معنا</b></font><br/>
        <font size="8" color="#666">Thank you for your business</font>
        </para>
        """
        story.append(Spacer(1, 20))
        story.append(Paragraph(footer_text, styles['Normal']))
        
        # بناء المستند
        doc.build(story)
        return filename
