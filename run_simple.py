#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام نقطة البيع البسيط والجميل
Simple and Beautiful POS System

تطبيق محسن لإدارة المبيعات مع:
- واجهة أبسط وأجمل
- إدارة الفئات والمنتجات
- فواتير احترافية مع QR Code
"""

import sys
import subprocess
import os

def check_python_version():
    """التحقق من إصدار Python"""
    if sys.version_info < (3, 6):
        print("❌ يتطلب Python 3.6 أو أحدث")
        print(f"الإصدار الحالي: {sys.version}")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
    return True

def install_requirements():
    """تثبيت المتطلبات المطلوبة"""
    requirements = [
        'reportlab',
        'qrcode[pil]',
        'Pillow'
    ]
    
    print("\n🔧 جاري تثبيت المتطلبات...")
    for requirement in requirements:
        try:
            print(f"   📦 تثبيت {requirement}...")
            subprocess.check_call([sys.executable, '-m', 'pip', 'install', requirement], 
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"   ✅ تم تثبيت {requirement}")
        except subprocess.CalledProcessError:
            print(f"   ❌ فشل في تثبيت {requirement}")
            return False
    
    print("✅ تم تثبيت جميع المتطلبات بنجاح!")
    return True

def check_requirements():
    """التحقق من وجود المتطلبات"""
    missing = []
    
    try:
        import tkinter
        print("✅ tkinter")
    except ImportError:
        print("❌ tkinter (مدمج مع Python)")
        missing.append("tkinter")
    
    try:
        import sqlite3
        print("✅ sqlite3")
    except ImportError:
        print("❌ sqlite3 (مدمج مع Python)")
        missing.append("sqlite3")
    
    try:
        import reportlab
        print("✅ reportlab")
    except ImportError:
        print("❌ reportlab")
        missing.append("reportlab")
    
    try:
        import qrcode
        print("✅ qrcode")
    except ImportError:
        print("❌ qrcode")
        missing.append("qrcode")
    
    try:
        from PIL import Image
        print("✅ Pillow")
    except ImportError:
        print("❌ Pillow")
        missing.append("Pillow")
    
    return missing

def main():
    """تشغيل النظام"""
    print("=" * 60)
    print("🛒 نظام نقطة البيع البسيط والجميل")
    print("   Simple and Beautiful POS System")
    print("=" * 60)
    
    # التحقق من إصدار Python
    if not check_python_version():
        input("\nاضغط Enter للخروج...")
        return
    
    print("\n🔍 فحص المتطلبات...")
    missing = check_requirements()
    
    if missing:
        print(f"\n⚠️  مكتبات مفقودة: {', '.join(missing)}")
        
        if 'tkinter' in missing or 'sqlite3' in missing:
            print("❌ tkinter أو sqlite3 مفقود - يرجى إعادة تثبيت Python")
            input("اضغط Enter للخروج...")
            return
        
        print("🔧 جاري تثبيت المكتبات المفقودة...")
        if not install_requirements():
            print("\n❌ فشل في تثبيت المتطلبات")
            print("يرجى تثبيتها يدوياً:")
            print("pip install reportlab qrcode[pil] Pillow")
            input("اضغط Enter للخروج...")
            return
    
    print("\n🚀 تشغيل النظام...")
    
    try:
        # التحقق من وجود الملفات المطلوبة
        required_files = ['simple_pos.py', 'database.py', 'invoice_generator.py']
        missing_files = [f for f in required_files if not os.path.exists(f)]
        
        if missing_files:
            print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
            input("اضغط Enter للخروج...")
            return
        
        # تشغيل النظام
        from simple_pos import main as run_simple_pos
        print("✅ تم تحميل النظام بنجاح!")
        print("\n" + "=" * 60)
        print("📋 تعليمات الاستخدام:")
        print("1️⃣  أضف فئة جديدة من القائمة اليسرى")
        print("2️⃣  اختر الفئة لعرض منتجاتها")
        print("3️⃣  أضف منتجات للفئة المختارة")
        print("4️⃣  انقر على المنتجات لإضافتها للسلة")
        print("5️⃣  أنشئ الفاتورة مع QR Code")
        print("=" * 60)
        print("🎉 استمتع بالاستخدام!")
        print()
        
        run_simple_pos()
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد الملفات: {e}")
        input("اضغط Enter للخروج...")
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
