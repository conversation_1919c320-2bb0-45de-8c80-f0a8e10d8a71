🛒 نظام نقطة البيع البسيط والجميل
=======================================

🚀 كيفية التشغيل:
-----------------
python run_simple.py
أو
python simple_pos.py

📋 المميزات الجديدة:
--------------------

✨ واجهة محسنة:
- تصميم ثلاثي الأعمدة أنيق
- ألوان جذابة ومتناسقة  
- أيقونات تعبيرية (إيموجي)
- تأثيرات تفاعلية جميلة

📂 إدارة الفئات:
- إضافة فئات بألوان مخصصة
- 6 ألوان جاهزة للاختيار
- عرض الفئات كأزرار ملونة
- 🗑️ حذف الفئات (مع التحقق من المنتجات)
- ❌ أزرار حذف سريع بجانب كل فئة
- فئات افتراضية جاهزة

🛍️ إدارة المنتجات:
- إضافة المنتجات حسب الفئة
- عرض المنتجات في بطاقات أنيقة
- بحث سريع في جميع المنتجات
- إدارة المخزون والكميات
- ✏️ تعديل المنتجات (اسم، سعر، كمية، باركود)
- 🗑️ حذف المنتجات (مع التحقق من الفواتير)
- أزرار سريعة على كل بطاقة منتج

🧾 فواتير احترافية:
- تصميم محسن مع ألوان وأيقونات
- QR Code حسب معايير ZATCA
- فتح الفاتورة تلقائياً بعد الإنشاء

🎯 كيفية الاستخدام:
-------------------

1️⃣ إدارة الفئات:
   إضافة فئة:
   - انقر "➕ إضافة فئة" (العمود الأيسر)
   - أدخل اسم الفئة ووصفها
   - اختر لوناً مناسباً
   - انقر "💾 حفظ"

   حذف فئة:
   - انقر "❌" بجانب الفئة للحذف السريع
   - أو انقر "🗑️ حذف فئة" لاختيار من قائمة
   - تأكيد الحذف (لا يمكن حذف فئة تحتوي على منتجات)

2️⃣ إدارة المنتجات:
   إضافة منتج:
   - اختر فئة من القائمة اليسرى أولاً ⚠️
   - انقر "➕ إضافة منتج" (العمود الأوسط)
   - أدخل اسم المنتج والسعر والكمية
   - أضف الباركود (اختياري)
   - انقر "💾 حفظ المنتج"

   تعديل منتج:
   - انقر "✏️" على بطاقة المنتج للتعديل السريع
   - أو انقر "✏️ تعديل منتج" لاختيار من قائمة
   - عدّل البيانات المطلوبة
   - انقر "💾 حفظ التغييرات"

   حذف منتج:
   - انقر "🗑️" على بطاقة المنتج للحذف السريع
   - أو انقر "🗑️ حذف منتج" لاختيار من قائمة
   - تأكيد الحذف (لا يمكن حذف منتج مستخدم في فواتير)

3️⃣ البيع والفواتير:
   - اختر فئة لعرض منتجاتها
   - انقر "➕" على المنتجات لإضافتها للسلة
   - راجع السلة في العمود الأيمن
   - أدخل اسم العميل (اختياري)
   - انقر "💳 إنشاء فاتورة"

🎨 الألوان المتاحة:
------------------
🟢 أخضر (#4CAF50) - مواد غذائية
🔵 أزرق (#2196F3) - مشروبات  
🟠 برتقالي (#FF9800) - وجبات خفيفة
🌸 وردي (#E91E63) - حلويات
🟣 بنفسجي (#9C27B0) - قرطاسية
🔷 سماوي (#00BCD4) - منظفات

📊 البيانات الافتراضية:
------------------------

الفئات الجاهزة:
- 🥤 مشروبات (أزرق)
- 🍿 وجبات خفيفة (برتقالي)
- 🍫 حلويات (وردي)
- 🥫 مواد غذائية (أخضر)
- ✏️ قرطاسية (بنفسجي)
- 🧽 منظفات (سماوي)

المنتجات التجريبية:
- كوكا كولا (3.50 ريال)
- عصير برتقال (4.00 ريال)
- شيبس ليز (5.50 ريال)
- بسكويت أوريو (8.00 ريال)
- شوكولاتة كيت كات (6.50 ريال)
- أرز بسمتي (25.00 ريال)
- قلم أزرق (2.50 ريال)
- صابون سائل (12.00 ريال)

🔧 الملفات المهمة:
------------------
- simple_pos.py: الواجهة الجديدة
- database.py: قاعدة البيانات المحسنة
- invoice_generator.py: مولد الفواتير المحسن
- run_simple.py: ملف التشغيل
- pos_system.db: قاعدة البيانات

⚠️ ملاحظات مهمة:
-----------------
1. يجب اختيار فئة أولاً قبل إضافة منتج
2. لا يمكن حذف فئة تحتوي على منتجات
3. لا يمكن حذف منتج مستخدم في فواتير
4. الألوان تساعد في تنظيم الفئات
5. البحث يعمل في جميع المنتجات
6. الفواتير تُحفظ تلقائياً كـ PDF
7. QR Code متوافق مع معايير ZATCA
8. أزرار الحذف السريع (❌ و 🗑️) على كل عنصر

🛠️ حل المشاكل:
---------------
❌ النظام لا يعمل:
   python --version (تأكد من Python 3.6+)
   python test_system.py

❌ مشاكل المكتبات:
   pip install reportlab qrcode[pil] Pillow

❌ مشاكل قاعدة البيانات:
   احذف pos_system.db وأعد التشغيل

🎉 الميزات الجديدة المضافة:
------------------------------
✅ واجهة أجمل وأبسط
✅ إدارة فئات بالألوان
✅ عرض المنتجات في بطاقات
✅ تأثيرات تفاعلية
✅ فواتير محسنة التصميم
✅ استخدام الإيموجي
✅ تنظيم أفضل للمحتوى
🆕 حذف الفئات مع التحقق من الأمان
🆕 حذف المنتجات مع التحقق من الأمان
🆕 تعديل المنتجات (اسم، سعر، كمية، باركود)
🆕 أزرار حذف سريع على كل عنصر
🆕 أزرار تعديل سريع على كل منتج
🆕 نوافذ تأكيد للعمليات الحساسة
🆕 رسائل خطأ واضحة ومفيدة

=======================================
🎯 استمتع بالنظام الجديد!
تم التطوير بـ ❤️ باستخدام Python
=======================================
