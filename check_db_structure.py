#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص هيكل قاعدة البيانات
"""

import sqlite3
import os

def check_database_structure():
    """فحص هيكل قاعدة البيانات"""
    db_file = "pos_system.db"
    
    if not os.path.exists(db_file):
        print(f"❌ ملف قاعدة البيانات {db_file} غير موجود")
        return
    
    print(f"🔍 فحص هيكل قاعدة البيانات: {db_file}")
    
    conn = sqlite3.connect(db_file)
    cursor = conn.cursor()
    
    # الحصول على قائمة الجداول
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()
    
    print(f"\n📋 الجداول الموجودة:")
    for table in tables:
        print(f"   - {table[0]}")
    
    # فحص هيكل جدول products
    print(f"\n🛍️ هيكل جدول products:")
    cursor.execute("PRAGMA table_info(products)")
    columns = cursor.fetchall()
    
    for col in columns:
        print(f"   - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
    
    # فحص هيكل جدول categories
    print(f"\n📂 هيكل جدول categories:")
    cursor.execute("PRAGMA table_info(categories)")
    columns = cursor.fetchall()
    
    for col in columns:
        print(f"   - {col[1]} ({col[2]}) {'NOT NULL' if col[3] else 'NULL'}")
    
    # عرض بعض البيانات
    print(f"\n📊 عينة من البيانات:")
    
    cursor.execute("SELECT COUNT(*) FROM categories")
    cat_count = cursor.fetchone()[0]
    print(f"   - عدد الفئات: {cat_count}")
    
    cursor.execute("SELECT COUNT(*) FROM products")
    prod_count = cursor.fetchone()[0]
    print(f"   - عدد المنتجات: {prod_count}")
    
    if prod_count > 0:
        print(f"\n🛍️ أول 3 منتجات:")
        cursor.execute("SELECT * FROM products LIMIT 3")
        products = cursor.fetchall()
        for prod in products:
            print(f"   - {prod}")
    
    conn.close()

if __name__ == "__main__":
    check_database_structure()
