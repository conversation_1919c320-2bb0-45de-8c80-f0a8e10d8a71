#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار مفصل لإضافة المنتجات
"""

from database import Database
import os

def debug_add_product():
    """اختبار مفصل لإضافة منتج"""
    print("🔍 اختبار مفصل لإضافة منتج...")
    
    # حذف قاعدة البيانات القديمة
    if os.path.exists("debug_test.db"):
        os.remove("debug_test.db")
    
    db = Database("debug_test.db")
    
    print("\n1️⃣ التحقق من الفئات المتاحة:")
    categories = db.get_all_categories()
    print(f"عدد الفئات: {len(categories)}")
    
    if not categories:
        print("❌ لا توجد فئات! إضافة فئة تجريبية...")
        category_id = db.add_category("فئة تجريبية", "وصف", "#4CAF50")
        print(f"تم إضافة فئة بـ ID: {category_id}")
        categories = db.get_all_categories()
    
    for cat in categories:
        print(f"   - الفئة: {cat[1]} (ID: {cat[0]})")
    
    print("\n2️⃣ محاولة إضافة منتج:")
    category_id = categories[0][0]
    
    # بيانات المنتج
    name = "منتج تجريبي"
    price = 25.50
    quantity = 10
    barcode = "TEST123"
    
    print(f"   - الاسم: {name}")
    print(f"   - السعر: {price}")
    print(f"   - الكمية: {quantity}")
    print(f"   - الباركود: {barcode}")
    print(f"   - الفئة ID: {category_id}")
    
    # محاولة الإضافة
    print("\n3️⃣ استدعاء add_product...")
    try:
        result = db.add_product(name, price, quantity, category_id, barcode)
        print(f"نتيجة add_product: {result}")
        print(f"نوع النتيجة: {type(result)}")
        
        if result is not None:
            print("✅ تم إرجاع نتيجة صحيحة")
        else:
            print("❌ تم إرجاع None")
            
    except Exception as e:
        print(f"❌ خطأ في add_product: {e}")
        return False
    
    print("\n4️⃣ التحقق من المنتجات في قاعدة البيانات:")
    products = db.get_all_products()
    print(f"عدد المنتجات: {len(products)}")
    
    found = False
    for prod in products:
        print(f"   - المنتج: {prod[1]} (ID: {prod[0]})")
        if prod[1] == name:
            found = True
            print(f"     ✅ تم العثور على المنتج المضاف!")
            print(f"     التفاصيل: {prod}")
    
    if not found:
        print("❌ لم يتم العثور على المنتج في قاعدة البيانات!")
        return False
    
    print("\n5️⃣ اختبار البحث:")
    search_results = db.search_product(name)
    print(f"نتائج البحث: {len(search_results)}")
    for result in search_results:
        print(f"   - {result[1]}")
    
    # تنظيف
    if os.path.exists("debug_test.db"):
        os.remove("debug_test.db")
    
    print("\n✅ الاختبار مكتمل!")
    return True

def test_database_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    print("🔍 اختبار الاتصال بقاعدة البيانات...")
    
    try:
        db = Database("connection_test.db")
        conn = db.get_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT 1")
        result = cursor.fetchone()
        conn.close()
        
        if result:
            print("✅ الاتصال بقاعدة البيانات يعمل")
            
        # تنظيف
        if os.path.exists("connection_test.db"):
            os.remove("connection_test.db")
            
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def main():
    print("=" * 60)
    print("🐛 تشخيص مشكلة إضافة المنتجات")
    print("=" * 60)
    
    # اختبار الاتصال
    if not test_database_connection():
        print("❌ فشل في اختبار الاتصال")
        return
    
    # اختبار إضافة المنتج
    if not debug_add_product():
        print("❌ فشل في اختبار إضافة المنتج")
        return
    
    print("\n" + "=" * 60)
    print("✅ جميع الاختبارات نجحت!")
    print("المشكلة قد تكون في الواجهة وليس في قاعدة البيانات")
    print("=" * 60)

if __name__ == "__main__":
    main()
