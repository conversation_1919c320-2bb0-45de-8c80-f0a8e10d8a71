#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام نقطة البيع الاحترافي - إصدار عصري
Professional Point of Sale System - Modern Edition
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
from database import Database
from invoice_generator import InvoiceGenerator
import os

class ModernPOSSystem:
    def __init__(self, root):
        self.root = root
        self.root.title("🛒 نظام نقطة البيع الاحترافي - Professional POS System")
        self.root.geometry("1600x1000")
        self.root.configure(bg='#f8fafc')
        self.root.state('zoomed')  # تكبير النافذة
        
        # ألوان النظام الاحترافية
        self.colors = {
            'primary': '#2563eb',      # أزرق احترافي
            'secondary': '#64748b',    # رمادي متوسط
            'success': '#10b981',      # أخضر نجاح
            'warning': '#f59e0b',      # برتقالي تحذير
            'danger': '#ef4444',       # أحمر خطر
            'info': '#06b6d4',         # سماوي معلومات
            'light': '#f8fafc',        # خلفية فاتحة
            'dark': '#1e293b',         # نص داكن
            'white': '#ffffff',        # أبيض نقي
            'border': '#e2e8f0',       # حدود فاتحة
            'hover': '#3b82f6',        # تأثير hover
            'gradient_start': '#667eea',
            'gradient_end': '#764ba2'
        }
        
        # إنشاء قاعدة البيانات
        self.db = Database()
        self.invoice_generator = InvoiceGenerator(self.db)
        
        # سلة التسوق
        self.cart = []
        self.selected_category = None
        
        # إنشاء الواجهة
        self.setup_modern_styles()
        self.create_modern_widgets()
        self.load_categories()
    
    def setup_modern_styles(self):
        """إعداد الأنماط العصرية"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # أنماط عصرية مخصصة
        style.configure('Title.TLabel', 
                       font=('Segoe UI', 24, 'bold'), 
                       background=self.colors['light'],
                       foreground=self.colors['primary'])
        
        style.configure('Header.TLabel', 
                       font=('Segoe UI', 14, 'bold'), 
                       background=self.colors['primary'],
                       foreground=self.colors['white'])
        
        style.configure('Modern.TButton', 
                       font=('Segoe UI', 11, 'bold'),
                       padding=(15, 10),
                       relief='flat')
        
        style.configure('Category.TButton', 
                       font=('Segoe UI', 12, 'bold'), 
                       padding=(12, 8),
                       relief='flat')
        
        style.configure('Action.TButton', 
                       font=('Segoe UI', 10, 'bold'), 
                       padding=(10, 6),
                       relief='flat')
        
        style.configure('Cart.TLabel', 
                       font=('Segoe UI', 12, 'bold'), 
                       background=self.colors['white'])
        
        style.configure('Total.TLabel', 
                       font=('Segoe UI', 16, 'bold'), 
                       background=self.colors['white'], 
                       foreground=self.colors['success'])
        
        style.configure('Modern.TFrame',
                       background=self.colors['white'],
                       relief='flat',
                       borderwidth=1)
        
        style.configure('Card.TFrame',
                       background=self.colors['white'],
                       relief='solid',
                       borderwidth=1)
    
    def create_modern_widgets(self):
        """إنشاء واجهة عصرية"""
        # شريط علوي احترافي
        self.create_header()
        
        # الإطار الرئيسي مع تدرج لوني
        main_container = tk.Frame(self.root, bg=self.colors['light'])
        main_container.pack(fill=tk.BOTH, expand=True, padx=25, pady=(0, 25))
        
        # الإطار الأساسي مع ظلال
        content_frame = tk.Frame(main_container, bg=self.colors['light'])
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # العمود الأول: الفئات (محسن)
        self.create_modern_categories_panel(content_frame)
        
        # العمود الثاني: المنتجات (محسن)
        self.create_modern_products_panel(content_frame)
        
        # العمود الثالث: السلة والفاتورة (محسن)
        self.create_modern_cart_panel(content_frame)
    
    def create_header(self):
        """إنشاء شريط علوي احترافي"""
        header_frame = tk.Frame(self.root, bg=self.colors['primary'], height=80)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        # إطار المحتوى
        header_content = tk.Frame(header_frame, bg=self.colors['primary'])
        header_content.pack(fill=tk.BOTH, expand=True, padx=30, pady=15)
        
        # العنوان الرئيسي
        title_frame = tk.Frame(header_content, bg=self.colors['primary'])
        title_frame.pack(side=tk.LEFT, fill=tk.Y)
        
        main_title = tk.Label(title_frame, 
                             text="🛒 نظام نقطة البيع الاحترافي",
                             font=('Segoe UI', 20, 'bold'),
                             bg=self.colors['primary'],
                             fg=self.colors['white'])
        main_title.pack(anchor='w')
        
        subtitle = tk.Label(title_frame,
                           text="Professional Point of Sale System",
                           font=('Segoe UI', 11),
                           bg=self.colors['primary'],
                           fg='#93c5fd')
        subtitle.pack(anchor='w')
        
        # معلومات النظام
        info_frame = tk.Frame(header_content, bg=self.colors['primary'])
        info_frame.pack(side=tk.RIGHT, fill=tk.Y)
        
        # عداد المنتجات والفئات
        self.products_count_label = tk.Label(info_frame,
                                           text="المنتجات: 0",
                                           font=('Segoe UI', 10),
                                           bg=self.colors['primary'],
                                           fg=self.colors['white'])
        self.products_count_label.pack(anchor='e')
        
        self.categories_count_label = tk.Label(info_frame,
                                             text="الفئات: 0",
                                             font=('Segoe UI', 10),
                                             bg=self.colors['primary'],
                                             fg=self.colors['white'])
        self.categories_count_label.pack(anchor='e')
    
    def create_modern_categories_panel(self, parent):
        """إنشاء لوحة الفئات العصرية"""
        # إطار خارجي مع ظل
        outer_frame = tk.Frame(parent, bg=self.colors['light'])
        outer_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 15))
        
        # بطاقة الفئات مع تصميم عصري
        categories_card = tk.Frame(outer_frame, 
                                 bg=self.colors['white'], 
                                 relief=tk.FLAT, 
                                 bd=0)
        categories_card.pack(fill=tk.BOTH, expand=True)
        
        # إضافة ظل للبطاقة
        shadow_frame = tk.Frame(outer_frame, bg='#e2e8f0', height=2)
        shadow_frame.pack(fill=tk.X, pady=(0, 5))
        
        # عنوان الفئات مع تدرج
        header_frame = tk.Frame(categories_card, bg=self.colors['primary'], height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        header_content = tk.Frame(header_frame, bg=self.colors['primary'])
        header_content.pack(expand=True, fill=tk.BOTH, padx=20, pady=15)
        
        tk.Label(header_content, text="📂 إدارة الفئات", 
                font=('Segoe UI', 16, 'bold'), 
                bg=self.colors['primary'], 
                fg=self.colors['white']).pack(side=tk.LEFT)
        
        # عداد الفئات
        self.categories_count = tk.Label(header_content, text="0", 
                                       font=('Segoe UI', 14, 'bold'),
                                       bg=self.colors['primary'], 
                                       fg='#93c5fd')
        self.categories_count.pack(side=tk.RIGHT)
        
        # أزرار إدارة الفئات مع تصميم عصري
        buttons_frame = tk.Frame(categories_card, bg=self.colors['white'])
        buttons_frame.pack(fill=tk.X, padx=15, pady=15)
        
        # زر إضافة فئة
        add_btn = tk.Button(buttons_frame, text="➕ إضافة فئة جديدة", 
                           command=self.add_category_dialog,
                           bg=self.colors['success'], 
                           fg=self.colors['white'],
                           font=('Segoe UI', 11, 'bold'),
                           relief=tk.FLAT, 
                           pady=12,
                           cursor='hand2',
                           activebackground='#059669')
        add_btn.pack(fill=tk.X, pady=(0, 8))
        
        # زر حذف فئة
        delete_btn = tk.Button(buttons_frame, text="🗑️ حذف فئة", 
                              command=self.delete_category_dialog,
                              bg=self.colors['danger'], 
                              fg=self.colors['white'],
                              font=('Segoe UI', 11, 'bold'),
                              relief=tk.FLAT, 
                              pady=12,
                              cursor='hand2',
                              activebackground='#dc2626')
        delete_btn.pack(fill=tk.X)
        
        # منطقة الفئات مع تمرير
        categories_container = tk.Frame(categories_card, bg=self.colors['white'])
        categories_container.pack(fill=tk.BOTH, expand=True, padx=15, pady=(0, 15))
        
        # Canvas للتمرير
        self.categories_canvas = tk.Canvas(categories_container, 
                                         bg=self.colors['white'],
                                         highlightthickness=0)
        categories_scrollbar = ttk.Scrollbar(categories_container, 
                                           orient="vertical", 
                                           command=self.categories_canvas.yview)
        self.categories_scrollable_frame = tk.Frame(self.categories_canvas, 
                                                  bg=self.colors['white'])
        
        self.categories_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.categories_canvas.configure(scrollregion=self.categories_canvas.bbox("all"))
        )
        
        self.categories_canvas.create_window((0, 0), window=self.categories_scrollable_frame, anchor="nw")
        self.categories_canvas.configure(yscrollcommand=categories_scrollbar.set)
        
        self.categories_canvas.pack(side="left", fill="both", expand=True)
        categories_scrollbar.pack(side="right", fill="y")
        
        # ربط عجلة الماوس
        self.categories_canvas.bind("<MouseWheel>", self._on_categories_mousewheel)
        
        # تحديد عرض اللوحة
        outer_frame.configure(width=320)

    def create_modern_products_panel(self, parent):
        """إنشاء لوحة المنتجات العصرية"""
        # إطار خارجي
        outer_frame = tk.Frame(parent, bg=self.colors['light'])
        outer_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 15))

        # بطاقة المنتجات
        products_card = tk.Frame(outer_frame,
                               bg=self.colors['white'],
                               relief=tk.FLAT,
                               bd=0)
        products_card.pack(fill=tk.BOTH, expand=True)

        # ظل البطاقة
        shadow_frame = tk.Frame(outer_frame, bg='#e2e8f0', height=2)
        shadow_frame.pack(fill=tk.X, pady=(0, 5))

        # عنوان المنتجات مع تصميم متدرج
        header_frame = tk.Frame(products_card, bg=self.colors['success'], height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        header_content = tk.Frame(header_frame, bg=self.colors['success'])
        header_content.pack(expand=True, fill=tk.BOTH, padx=20, pady=15)

        self.products_title = tk.Label(header_content, text="🛍️ إدارة المنتجات",
                                     font=('Segoe UI', 16, 'bold'),
                                     bg=self.colors['success'],
                                     fg=self.colors['white'])
        self.products_title.pack(side=tk.LEFT)

        # عداد المنتجات
        self.products_count = tk.Label(header_content, text="0",
                                     font=('Segoe UI', 14, 'bold'),
                                     bg=self.colors['success'],
                                     fg='#bbf7d0')
        self.products_count.pack(side=tk.RIGHT)

        # شريط الأدوات العصري
        toolbar_frame = tk.Frame(products_card, bg=self.colors['white'])
        toolbar_frame.pack(fill=tk.X, padx=20, pady=15)

        # الأزرار اليسرى
        left_buttons = tk.Frame(toolbar_frame, bg=self.colors['white'])
        left_buttons.pack(side=tk.LEFT)

        # زر إضافة منتج
        add_product_btn = tk.Button(left_buttons, text="➕ إضافة منتج",
                                   command=self.add_product_dialog,
                                   bg=self.colors['primary'],
                                   fg=self.colors['white'],
                                   font=('Segoe UI', 10, 'bold'),
                                   relief=tk.FLAT,
                                   padx=15, pady=8,
                                   cursor='hand2',
                                   activebackground=self.colors['hover'])
        add_product_btn.pack(side=tk.LEFT, padx=(0, 8))

        # زر تعديل منتج
        edit_product_btn = tk.Button(left_buttons, text="✏️ تعديل",
                                    command=self.edit_product_dialog,
                                    bg=self.colors['warning'],
                                    fg=self.colors['white'],
                                    font=('Segoe UI', 10, 'bold'),
                                    relief=tk.FLAT,
                                    padx=15, pady=8,
                                    cursor='hand2',
                                    activebackground='#d97706')
        edit_product_btn.pack(side=tk.LEFT, padx=(0, 8))

        # زر حذف منتج
        delete_product_btn = tk.Button(left_buttons, text="🗑️ حذف",
                                      command=self.delete_product_dialog,
                                      bg=self.colors['danger'],
                                      fg=self.colors['white'],
                                      font=('Segoe UI', 10, 'bold'),
                                      relief=tk.FLAT,
                                      padx=15, pady=8,
                                      cursor='hand2',
                                      activebackground='#dc2626')
        delete_product_btn.pack(side=tk.LEFT)

        # شريط البحث العصري
        search_frame = tk.Frame(toolbar_frame, bg=self.colors['white'])
        search_frame.pack(side=tk.RIGHT)

        search_container = tk.Frame(search_frame, bg='#f1f5f9', relief=tk.FLAT, bd=1)
        search_container.pack()

        search_icon = tk.Label(search_container, text="🔍",
                              bg='#f1f5f9',
                              font=('Segoe UI', 12))
        search_icon.pack(side=tk.LEFT, padx=(10, 5))

        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_container,
                               textvariable=self.search_var,
                               width=25,
                               font=('Segoe UI', 11),
                               bg='#f1f5f9',
                               relief=tk.FLAT,
                               bd=0)
        search_entry.pack(side=tk.LEFT, padx=(0, 10), pady=8)
        search_entry.bind('<KeyRelease>', self.search_products)

        # منطقة المنتجات مع تمرير عصري
        products_container = tk.Frame(products_card, bg=self.colors['white'])
        products_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=(0, 20))

        # Canvas للتمرير مع تصميم عصري
        self.products_canvas = tk.Canvas(products_container,
                                       bg='#f8fafc',
                                       highlightthickness=0,
                                       relief=tk.FLAT)

        # شريط تمرير عصري
        products_scrollbar = ttk.Scrollbar(products_container,
                                         orient="vertical",
                                         command=self.products_canvas.yview)

        self.products_scrollable_frame = tk.Frame(self.products_canvas,
                                                bg='#f8fafc')

        self.products_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.products_canvas.configure(scrollregion=self.products_canvas.bbox("all"))
        )

        self.products_canvas.create_window((0, 0), window=self.products_scrollable_frame, anchor="nw")
        self.products_canvas.configure(yscrollcommand=products_scrollbar.set)

        self.products_canvas.pack(side="left", fill="both", expand=True)
        products_scrollbar.pack(side="right", fill="y")

        # ربط عجلة الماوس
        self.products_canvas.bind("<MouseWheel>", self._on_products_mousewheel)

    def create_modern_cart_panel(self, parent):
        """إنشاء لوحة السلة والفاتورة العصرية"""
        # إطار خارجي
        outer_frame = tk.Frame(parent, bg=self.colors['light'])
        outer_frame.pack(side=tk.RIGHT, fill=tk.Y)

        # بطاقة السلة
        cart_card = tk.Frame(outer_frame,
                           bg=self.colors['white'],
                           relief=tk.FLAT,
                           bd=0)
        cart_card.pack(fill=tk.BOTH, expand=True)

        # ظل البطاقة
        shadow_frame = tk.Frame(outer_frame, bg='#e2e8f0', height=2)
        shadow_frame.pack(fill=tk.X, pady=(0, 5))

        # عنوان السلة مع تدرج برتقالي
        header_frame = tk.Frame(cart_card, bg=self.colors['warning'], height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        header_content = tk.Frame(header_frame, bg=self.colors['warning'])
        header_content.pack(expand=True, fill=tk.BOTH, padx=20, pady=15)

        tk.Label(header_content, text="🛒 سلة التسوق",
                font=('Segoe UI', 16, 'bold'),
                bg=self.colors['warning'],
                fg=self.colors['white']).pack(side=tk.LEFT)

        # عداد العناصر
        self.cart_count = tk.Label(header_content, text="0",
                                 font=('Segoe UI', 14, 'bold'),
                                 bg=self.colors['warning'],
                                 fg='#fef3c7')
        self.cart_count.pack(side=tk.RIGHT)

        # منطقة السلة مع تمرير
        cart_container = tk.Frame(cart_card, bg=self.colors['white'])
        cart_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=15)

        # Canvas للسلة
        self.cart_canvas = tk.Canvas(cart_container,
                                   bg='#f8fafc',
                                   highlightthickness=0,
                                   relief=tk.FLAT)
        cart_scrollbar = ttk.Scrollbar(cart_container,
                                     orient="vertical",
                                     command=self.cart_canvas.yview)
        self.cart_scrollable_frame = tk.Frame(self.cart_canvas,
                                            bg='#f8fafc')

        self.cart_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.cart_canvas.configure(scrollregion=self.cart_canvas.bbox("all"))
        )

        self.cart_canvas.create_window((0, 0), window=self.cart_scrollable_frame, anchor="nw")
        self.cart_canvas.configure(yscrollcommand=cart_scrollbar.set)

        self.cart_canvas.pack(side="left", fill="both", expand=True)
        cart_scrollbar.pack(side="right", fill="y")

        # ربط عجلة الماوس
        self.cart_canvas.bind("<MouseWheel>", self._on_cart_mousewheel)

        # منطقة الإجمالي العصرية
        total_container = tk.Frame(cart_card, bg=self.colors['white'])
        total_container.pack(fill=tk.X, padx=20, pady=(0, 15))

        total_card = tk.Frame(total_container,
                            bg='#f0fdf4',
                            relief=tk.FLAT,
                            bd=1)
        total_card.pack(fill=tk.X, pady=(0, 15))

        total_content = tk.Frame(total_card, bg='#f0fdf4')
        total_content.pack(fill=tk.X, padx=15, pady=15)

        tk.Label(total_content, text="💰 الإجمالي:",
                font=('Segoe UI', 14, 'bold'),
                bg='#f0fdf4',
                fg=self.colors['dark']).pack(side=tk.LEFT)

        self.total_label = tk.Label(total_content, text="0.00 ريال",
                                  font=('Segoe UI', 18, 'bold'),
                                  bg='#f0fdf4',
                                  fg=self.colors['success'])
        self.total_label.pack(side=tk.RIGHT)

        # حقل اسم العميل العصري
        customer_container = tk.Frame(cart_card, bg=self.colors['white'])
        customer_container.pack(fill=tk.X, padx=20, pady=(0, 15))

        tk.Label(customer_container, text="👤 اسم العميل:",
                font=('Segoe UI', 11, 'bold'),
                bg=self.colors['white'],
                fg=self.colors['dark']).pack(anchor='w', pady=(0, 5))

        customer_entry_frame = tk.Frame(customer_container, bg='#f1f5f9', relief=tk.FLAT, bd=1)
        customer_entry_frame.pack(fill=tk.X)

        self.customer_name_var = tk.StringVar()
        customer_entry = tk.Entry(customer_entry_frame,
                                textvariable=self.customer_name_var,
                                font=('Segoe UI', 11),
                                bg='#f1f5f9',
                                relief=tk.FLAT,
                                bd=0)
        customer_entry.pack(fill=tk.X, padx=10, pady=8)

        # أزرار العمليات العصرية
        buttons_container = tk.Frame(cart_card, bg=self.colors['white'])
        buttons_container.pack(fill=tk.X, padx=20, pady=(0, 20))

        # زر إنشاء فاتورة
        invoice_btn = tk.Button(buttons_container, text="💳 إنشاء فاتورة",
                               command=self.create_invoice,
                               bg=self.colors['primary'],
                               fg=self.colors['white'],
                               font=('Segoe UI', 12, 'bold'),
                               relief=tk.FLAT,
                               pady=15,
                               cursor='hand2',
                               activebackground=self.colors['hover'])
        invoice_btn.pack(fill=tk.X, pady=(0, 10))

        # زر مسح السلة
        clear_btn = tk.Button(buttons_container, text="🗑️ مسح السلة",
                             command=self.clear_cart,
                             bg=self.colors['secondary'],
                             fg=self.colors['white'],
                             font=('Segoe UI', 11, 'bold'),
                             relief=tk.FLAT,
                             pady=12,
                             cursor='hand2',
                             activebackground='#475569')
        clear_btn.pack(fill=tk.X)

        # تحديد عرض اللوحة
        outer_frame.configure(width=400)

    # دوال التمرير
    def _on_products_mousewheel(self, event):
        """التعامل مع عجلة الماوس للمنتجات"""
        self.products_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def _on_categories_mousewheel(self, event):
        """التعامل مع عجلة الماوس للفئات"""
        self.categories_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    def _on_cart_mousewheel(self, event):
        """التعامل مع عجلة الماوس للسلة"""
        self.cart_canvas.yview_scroll(int(-1*(event.delta/120)), "units")

    # دوال أساسية مؤقتة (ستحتاج للتطوير)
    def load_categories(self):
        """تحميل الفئات بتصميم عصري"""
        # مسح الفئات الحالية
        for widget in self.categories_scrollable_frame.winfo_children():
            widget.destroy()

        categories = self.db.get_all_categories()

        # تحديث عداد الفئات
        self.categories_count.config(text=str(len(categories)))
        self.categories_count_label.config(text=f"الفئات: {len(categories)}")

        for category in categories:
            cat_id, name, description, color, created_at = category

            # بطاقة الفئة العصرية
            category_card = tk.Frame(self.categories_scrollable_frame,
                                   bg=self.colors['white'],
                                   relief=tk.SOLID,
                                   bd=1,
                                   cursor='hand2')
            category_card.pack(fill=tk.X, pady=8, padx=5)

            # شريط ملون علوي
            color_bar = tk.Frame(category_card, bg=color or self.colors['primary'], height=4)
            color_bar.pack(fill=tk.X)

            # محتوى البطاقة
            content_frame = tk.Frame(category_card, bg=self.colors['white'])
            content_frame.pack(fill=tk.X, padx=15, pady=12)

            # الصف العلوي: اسم الفئة وزر الحذف
            top_row = tk.Frame(content_frame, bg=self.colors['white'])
            top_row.pack(fill=tk.X, pady=(0, 5))

            # اسم الفئة
            name_label = tk.Label(top_row, text=name,
                                font=('Segoe UI', 13, 'bold'),
                                bg=self.colors['white'],
                                fg=self.colors['dark'])
            name_label.pack(side=tk.LEFT)

            # زر حذف سريع
            delete_btn = tk.Button(top_row, text="🗑️",
                                 font=('Segoe UI', 10),
                                 bg=self.colors['danger'],
                                 fg=self.colors['white'],
                                 relief=tk.FLAT,
                                 width=3,
                                 cursor='hand2',
                                 command=lambda c_id=cat_id: self.quick_delete_category(c_id))
            delete_btn.pack(side=tk.RIGHT)

            # الوصف
            if description:
                desc_label = tk.Label(content_frame, text=description,
                                    font=('Segoe UI', 10),
                                    bg=self.colors['white'],
                                    fg=self.colors['secondary'],
                                    wraplength=250)
                desc_label.pack(anchor='w')

            # ربط النقر لاختيار الفئة
            def bind_click(widget, cat_id, cat_name):
                widget.bind("<Button-1>", lambda e: self.select_category(cat_id, cat_name))

            bind_click(category_card, cat_id, name)
            bind_click(content_frame, cat_id, name)
            bind_click(name_label, cat_id, name)
            if description:
                bind_click(desc_label, cat_id, name)

            # تأثير hover
            def on_enter(e):
                category_card.config(bg='#f1f5f9')
                content_frame.config(bg='#f1f5f9')
                name_label.config(bg='#f1f5f9')
                if description:
                    desc_label.config(bg='#f1f5f9')

            def on_leave(e):
                category_card.config(bg=self.colors['white'])
                content_frame.config(bg=self.colors['white'])
                name_label.config(bg=self.colors['white'])
                if description:
                    desc_label.config(bg=self.colors['white'])

            category_card.bind("<Enter>", on_enter)
            category_card.bind("<Leave>", on_leave)

    def select_category(self, category_id, category_name):
        """اختيار فئة وعرض منتجاتها"""
        self.selected_category = category_id
        self.products_title.configure(text=f"🛍️ {category_name}")
        self.load_products()

    def load_products(self):
        """تحميل المنتجات بتصميم عصري"""
        # مسح المنتجات الحالية
        for widget in self.products_scrollable_frame.winfo_children():
            widget.destroy()

        if self.selected_category:
            products = self.db.get_products_by_category(self.selected_category)
        else:
            products = self.db.get_all_products()

        # تحديث عداد المنتجات
        self.products_count.config(text=str(len(products)))
        self.products_count_label.config(text=f"المنتجات: {len(products)}")

        # ترتيب المنتجات في شبكة عصرية
        row = 0
        col = 0
        max_cols = 3

        for product in products:
            if len(product) >= 8:
                product_id, name, price, quantity, barcode, category_id, created_at, category_name, category_color = product
            else:
                product_id, name, price, quantity, barcode, category_id, created_at = product

            # بطاقة المنتج العصرية
            product_card = tk.Frame(self.products_scrollable_frame,
                                  bg=self.colors['white'],
                                  relief=tk.SOLID,
                                  bd=1,
                                  width=220,
                                  height=180)
            product_card.grid(row=row, column=col, padx=12, pady=12, sticky='nsew')
            product_card.grid_propagate(False)

            # شريط علوي ملون
            header_bar = tk.Frame(product_card, bg=self.colors['primary'], height=6)
            header_bar.pack(fill=tk.X)

            # محتوى البطاقة
            content_frame = tk.Frame(product_card, bg=self.colors['white'])
            content_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=12)

            # اسم المنتج
            name_label = tk.Label(content_frame, text=name,
                                font=('Segoe UI', 12, 'bold'),
                                bg=self.colors['white'],
                                fg=self.colors['dark'],
                                wraplength=190)
            name_label.pack(anchor='w', pady=(0, 8))

            # معلومات المنتج
            info_frame = tk.Frame(content_frame, bg=self.colors['white'])
            info_frame.pack(fill=tk.X, pady=(0, 12))

            # السعر
            price_frame = tk.Frame(info_frame, bg=self.colors['white'])
            price_frame.pack(fill=tk.X, pady=(0, 4))

            tk.Label(price_frame, text="💰",
                    font=('Segoe UI', 11),
                    bg=self.colors['white']).pack(side=tk.LEFT)

            price_label = tk.Label(price_frame, text=f"{price:.2f} ريال",
                                 font=('Segoe UI', 11, 'bold'),
                                 bg=self.colors['white'],
                                 fg=self.colors['success'])
            price_label.pack(side=tk.LEFT, padx=(5, 0))

            # الكمية
            quantity_frame = tk.Frame(info_frame, bg=self.colors['white'])
            quantity_frame.pack(fill=tk.X)

            tk.Label(quantity_frame, text="📦",
                    font=('Segoe UI', 11),
                    bg=self.colors['white']).pack(side=tk.LEFT)

            quantity_color = self.colors['success'] if quantity > 10 else self.colors['warning'] if quantity > 0 else self.colors['danger']
            quantity_label = tk.Label(quantity_frame, text=f"الكمية: {quantity}",
                                    font=('Segoe UI', 10),
                                    bg=self.colors['white'],
                                    fg=quantity_color)
            quantity_label.pack(side=tk.LEFT, padx=(5, 0))

            # أزرار العمليات العصرية
            buttons_frame = tk.Frame(content_frame, bg=self.colors['white'])
            buttons_frame.pack(fill=tk.X, pady=(8, 0))

            # زر الإضافة للسلة (كبير ومميز)
            if quantity > 0:
                add_btn = tk.Button(buttons_frame, text="➕ إضافة للسلة",
                                  font=('Segoe UI', 10, 'bold'),
                                  bg=self.colors['success'],
                                  fg=self.colors['white'],
                                  relief=tk.FLAT,
                                  cursor='hand2',
                                  activebackground='#059669',
                                  command=lambda p=product: self.add_to_cart(p))
                add_btn.pack(fill=tk.X, pady=(0, 6))
            else:
                tk.Label(buttons_frame, text="❌ نفد المخزون",
                        font=('Segoe UI', 10, 'bold'),
                        bg=self.colors['danger'],
                        fg=self.colors['white']).pack(fill=tk.X, pady=(0, 6))

            # أزرار صغيرة للتعديل والحذف
            small_buttons = tk.Frame(buttons_frame, bg=self.colors['white'])
            small_buttons.pack(fill=tk.X)

            # زر التعديل
            edit_btn = tk.Button(small_buttons, text="✏️ تعديل",
                               font=('Segoe UI', 9, 'bold'),
                               bg=self.colors['warning'],
                               fg=self.colors['white'],
                               relief=tk.FLAT,
                               cursor='hand2',
                               activebackground='#d97706',
                               command=lambda p_id=product_id: self.edit_product_quick(p_id))
            edit_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 3))

            # زر الحذف
            delete_btn = tk.Button(small_buttons, text="🗑️ حذف",
                                 font=('Segoe UI', 9, 'bold'),
                                 bg=self.colors['danger'],
                                 fg=self.colors['white'],
                                 relief=tk.FLAT,
                                 cursor='hand2',
                                 activebackground='#dc2626',
                                 command=lambda p_id=product_id: self.delete_product_quick(p_id))
            delete_btn.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(3, 0))

            # تأثير hover للبطاقة
            def on_card_enter(e, card=product_card, content=content_frame, name=name_label):
                card.config(relief=tk.RAISED, bd=2)
                content.config(bg='#f8fafc')
                name.config(bg='#f8fafc')

            def on_card_leave(e, card=product_card, content=content_frame, name=name_label):
                card.config(relief=tk.SOLID, bd=1)
                content.config(bg=self.colors['white'])
                name.config(bg=self.colors['white'])

            product_card.bind("<Enter>", on_card_enter)
            product_card.bind("<Leave>", on_card_leave)

            # الانتقال للعمود التالي
            col += 1
            if col >= max_cols:
                col = 0
                row += 1

        # تكوين الأعمدة
        for i in range(max_cols):
            self.products_scrollable_frame.grid_columnconfigure(i, weight=1)

    def add_category_dialog(self):
        """إضافة فئة جديدة"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة فئة جديدة")
        dialog.geometry("400x300")
        dialog.configure(bg=self.colors['white'])
        dialog.resizable(False, False)

        # جعل النافذة في المقدمة
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title_label = tk.Label(dialog, text="➕ إضافة فئة جديدة",
                              font=('Segoe UI', 16, 'bold'),
                              bg=self.colors['white'],
                              fg=self.colors['primary'])
        title_label.pack(pady=20)

        # إطار المحتوى
        content_frame = tk.Frame(dialog, bg=self.colors['white'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=(0, 20))

        # اسم الفئة
        tk.Label(content_frame, text="📝 اسم الفئة:",
                font=('Segoe UI', 12, 'bold'),
                bg=self.colors['white'],
                fg=self.colors['dark']).pack(anchor='w', pady=(0, 5))

        name_entry = tk.Entry(content_frame, font=('Segoe UI', 11), width=30)
        name_entry.pack(fill=tk.X, pady=(0, 15))
        name_entry.focus()

        # وصف الفئة
        tk.Label(content_frame, text="📄 وصف الفئة:",
                font=('Segoe UI', 12, 'bold'),
                bg=self.colors['white'],
                fg=self.colors['dark']).pack(anchor='w', pady=(0, 5))

        desc_entry = tk.Entry(content_frame, font=('Segoe UI', 11), width=30)
        desc_entry.pack(fill=tk.X, pady=(0, 15))

        # لون الفئة
        tk.Label(content_frame, text="🎨 لون الفئة:",
                font=('Segoe UI', 12, 'bold'),
                bg=self.colors['white'],
                fg=self.colors['dark']).pack(anchor='w', pady=(0, 5))

        colors_frame = tk.Frame(content_frame, bg=self.colors['white'])
        colors_frame.pack(fill=tk.X, pady=(0, 20))

        color_var = tk.StringVar(value=self.colors['primary'])
        color_options = [
            ('#10b981', '🟢 أخضر'),
            ('#2563eb', '🔵 أزرق'),
            ('#f59e0b', '🟠 برتقالي'),
            ('#e11d48', '🔴 أحمر'),
            ('#8b5cf6', '🟣 بنفسجي'),
            ('#06b6d4', '🔷 سماوي')
        ]

        for i, (color_code, color_name) in enumerate(color_options):
            row = i // 3
            col = i % 3

            color_btn = tk.Radiobutton(colors_frame, text=color_name,
                                     variable=color_var, value=color_code,
                                     font=('Segoe UI', 10),
                                     bg=self.colors['white'],
                                     activebackground=self.colors['white'])
            color_btn.grid(row=row, column=col, sticky='w', padx=(0, 20), pady=2)

        # أزرار العمليات
        buttons_frame = tk.Frame(content_frame, bg=self.colors['white'])
        buttons_frame.pack(fill=tk.X, pady=(10, 0))

        def save_category():
            name = name_entry.get().strip()
            description = desc_entry.get().strip()
            color = color_var.get()

            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم الفئة")
                return

            try:
                result = self.db.add_category(name, description, color)
                if result is not None:
                    messagebox.showinfo("نجح", f"تم إضافة الفئة '{name}' بنجاح!")
                    self.load_categories()
                    dialog.destroy()
                else:
                    messagebox.showerror("خطأ", "فشل في إضافة الفئة")
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}")

        # زر الحفظ
        save_btn = tk.Button(buttons_frame, text="💾 حفظ الفئة",
                           command=save_category,
                           bg=self.colors['success'],
                           fg=self.colors['white'],
                           font=('Segoe UI', 11, 'bold'),
                           relief=tk.FLAT,
                           pady=10,
                           cursor='hand2')
        save_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        # زر الإلغاء
        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء",
                             command=dialog.destroy,
                             bg=self.colors['secondary'],
                             fg=self.colors['white'],
                             font=('Segoe UI', 11, 'bold'),
                             relief=tk.FLAT,
                             pady=10,
                             cursor='hand2')
        cancel_btn.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))

        # ربط Enter للحفظ
        dialog.bind('<Return>', lambda e: save_category())

    def quick_delete_category(self, category_id):
        """حذف سريع للفئة"""
        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذه الفئة؟"):
            try:
                self.db.delete_category(category_id)
                messagebox.showinfo("نجح", "تم حذف الفئة بنجاح!")
                self.load_categories()
                self.load_products()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف الفئة: {str(e)}")

    def delete_category_dialog(self):
        """حذف فئة"""
        categories = self.db.get_all_categories()
        if not categories:
            messagebox.showwarning("تحذير", "لا توجد فئات للحذف")
            return

        dialog = tk.Toplevel(self.root)
        dialog.title("حذف فئة")
        dialog.geometry("350x200")
        dialog.configure(bg=self.colors['white'])
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        tk.Label(dialog, text="🗑️ اختر الفئة المراد حذفها",
                font=('Segoe UI', 14, 'bold'),
                bg=self.colors['white'],
                fg=self.colors['danger']).pack(pady=20)

        category_var = tk.StringVar()
        category_combo = ttk.Combobox(dialog, textvariable=category_var,
                                    font=('Segoe UI', 11), width=30)
        category_combo['values'] = [f"{cat[1]} (ID: {cat[0]})" for cat in categories]
        category_combo.pack(pady=10)

        def delete_selected():
            if not category_var.get():
                messagebox.showwarning("تحذير", "يرجى اختيار فئة")
                return

            category_id = int(category_var.get().split("ID: ")[1].split(")")[0])
            self.quick_delete_category(category_id)
            dialog.destroy()

        buttons_frame = tk.Frame(dialog, bg=self.colors['white'])
        buttons_frame.pack(pady=20)

        tk.Button(buttons_frame, text="🗑️ حذف",
                 command=delete_selected,
                 bg=self.colors['danger'],
                 fg=self.colors['white'],
                 font=('Segoe UI', 11, 'bold'),
                 relief=tk.FLAT,
                 padx=20, pady=8,
                 cursor='hand2').pack(side=tk.LEFT, padx=(0, 10))

        tk.Button(buttons_frame, text="❌ إلغاء",
                 command=dialog.destroy,
                 bg=self.colors['secondary'],
                 fg=self.colors['white'],
                 font=('Segoe UI', 11, 'bold'),
                 relief=tk.FLAT,
                 padx=20, pady=8,
                 cursor='hand2').pack(side=tk.LEFT)

    def add_product_dialog(self):
        """إضافة منتج جديد"""
        if not self.selected_category:
            messagebox.showwarning("تحذير", "يرجى اختيار فئة أولاً")
            return

        # الحصول على معلومات الفئة المختارة
        categories = self.db.get_all_categories()
        category_name = "غير محدد"
        for cat in categories:
            if cat[0] == self.selected_category:
                category_name = cat[1]
                break

        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة منتج جديد")
        dialog.geometry("450x400")
        dialog.configure(bg=self.colors['white'])
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title_label = tk.Label(dialog, text="➕ إضافة منتج جديد",
                              font=('Segoe UI', 16, 'bold'),
                              bg=self.colors['white'],
                              fg=self.colors['primary'])
        title_label.pack(pady=20)

        # معلومات الفئة
        tk.Label(dialog, text=f"الفئة: {category_name}",
                font=('Segoe UI', 11),
                bg=self.colors['white'],
                fg=self.colors['secondary']).pack()

        # إطار المحتوى
        content_frame = tk.Frame(dialog, bg=self.colors['white'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)

        # اسم المنتج
        tk.Label(content_frame, text="📝 اسم المنتج:",
                font=('Segoe UI', 12, 'bold'),
                bg=self.colors['white'],
                fg=self.colors['dark']).pack(anchor='w', pady=(0, 5))

        name_entry = tk.Entry(content_frame, font=('Segoe UI', 11), width=35)
        name_entry.pack(fill=tk.X, pady=(0, 15))
        name_entry.focus()

        # السعر
        tk.Label(content_frame, text="💰 السعر (ريال):",
                font=('Segoe UI', 12, 'bold'),
                bg=self.colors['white'],
                fg=self.colors['dark']).pack(anchor='w', pady=(0, 5))

        price_entry = tk.Entry(content_frame, font=('Segoe UI', 11), width=35)
        price_entry.pack(fill=tk.X, pady=(0, 15))

        # الكمية
        tk.Label(content_frame, text="📦 الكمية:",
                font=('Segoe UI', 12, 'bold'),
                bg=self.colors['white'],
                fg=self.colors['dark']).pack(anchor='w', pady=(0, 5))

        quantity_entry = tk.Entry(content_frame, font=('Segoe UI', 11), width=35)
        quantity_entry.pack(fill=tk.X, pady=(0, 15))

        # الباركود
        tk.Label(content_frame, text="🏷️ الباركود (اختياري):",
                font=('Segoe UI', 12, 'bold'),
                bg=self.colors['white'],
                fg=self.colors['dark']).pack(anchor='w', pady=(0, 5))

        barcode_entry = tk.Entry(content_frame, font=('Segoe UI', 11), width=35)
        barcode_entry.pack(fill=tk.X, pady=(0, 20))

        # أزرار العمليات
        buttons_frame = tk.Frame(content_frame, bg=self.colors['white'])
        buttons_frame.pack(fill=tk.X)

        def save_product():
            name = name_entry.get().strip()
            price_str = price_entry.get().strip()
            quantity_str = quantity_entry.get().strip()
            barcode = barcode_entry.get().strip()

            # التحقق من البيانات
            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم المنتج")
                return

            try:
                price = float(price_str)
                if price <= 0:
                    messagebox.showerror("خطأ", "يرجى إدخال سعر صحيح")
                    return
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال سعر صحيح")
                return

            try:
                quantity = int(quantity_str)
                if quantity < 0:
                    messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة")
                    return
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة")
                return

            # محاولة إضافة المنتج
            try:
                result = self.db.add_product(name, price, quantity, self.selected_category, barcode)

                if result is not None:
                    messagebox.showinfo("نجح", f"تم إضافة المنتج بنجاح!\n\nاسم المنتج: {name}\nالسعر: {price} ريال\nالكمية: {quantity}")
                    self.load_products()
                    dialog.destroy()
                else:
                    messagebox.showerror("خطأ", "فشل في إضافة المنتج\n\nالأسباب المحتملة:\n• الباركود مكرر\n• خطأ في البيانات\n• مشكلة في قاعدة البيانات")

            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ غير متوقع:\n{str(e)}\n\nيرجى المحاولة مرة أخرى")

        # زر الحفظ
        save_btn = tk.Button(buttons_frame, text="💾 حفظ المنتج",
                           command=save_product,
                           bg=self.colors['success'],
                           fg=self.colors['white'],
                           font=('Segoe UI', 11, 'bold'),
                           relief=tk.FLAT,
                           pady=10,
                           cursor='hand2')
        save_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        # زر الإلغاء
        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء",
                             command=dialog.destroy,
                             bg=self.colors['secondary'],
                             fg=self.colors['white'],
                             font=('Segoe UI', 11, 'bold'),
                             relief=tk.FLAT,
                             pady=10,
                             cursor='hand2')
        cancel_btn.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))

        # ربط Enter للحفظ
        dialog.bind('<Return>', lambda e: save_product())

    def edit_product_dialog(self):
        """تعديل منتج"""
        products = self.db.get_all_products()
        if not products:
            messagebox.showwarning("تحذير", "لا توجد منتجات للتعديل")
            return

        # نافذة اختيار المنتج
        dialog = tk.Toplevel(self.root)
        dialog.title("اختيار منتج للتعديل")
        dialog.geometry("400x200")
        dialog.configure(bg=self.colors['white'])
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        tk.Label(dialog, text="✏️ اختر المنتج المراد تعديله",
                font=('Segoe UI', 14, 'bold'),
                bg=self.colors['white'],
                fg=self.colors['warning']).pack(pady=20)

        product_var = tk.StringVar()
        product_combo = ttk.Combobox(dialog, textvariable=product_var,
                                   font=('Segoe UI', 11), width=40)
        product_combo['values'] = [f"{prod[1]} - {prod[2]:.2f} ريال (ID: {prod[0]})" for prod in products]
        product_combo.pack(pady=10)

        def edit_selected():
            if not product_var.get():
                messagebox.showwarning("تحذير", "يرجى اختيار منتج")
                return

            product_id = int(product_var.get().split("ID: ")[1].split(")")[0])
            dialog.destroy()
            self.edit_product_form(product_id)

        buttons_frame = tk.Frame(dialog, bg=self.colors['white'])
        buttons_frame.pack(pady=20)

        tk.Button(buttons_frame, text="✏️ تعديل",
                 command=edit_selected,
                 bg=self.colors['warning'],
                 fg=self.colors['white'],
                 font=('Segoe UI', 11, 'bold'),
                 relief=tk.FLAT,
                 padx=20, pady=8,
                 cursor='hand2').pack(side=tk.LEFT, padx=(0, 10))

        tk.Button(buttons_frame, text="❌ إلغاء",
                 command=dialog.destroy,
                 bg=self.colors['secondary'],
                 fg=self.colors['white'],
                 font=('Segoe UI', 11, 'bold'),
                 relief=tk.FLAT,
                 padx=20, pady=8,
                 cursor='hand2').pack(side=tk.LEFT)

    def edit_product_quick(self, product_id):
        """تعديل سريع للمنتج"""
        self.edit_product_form(product_id)

    def edit_product_form(self, product_id):
        """نموذج تعديل المنتج"""
        # الحصول على بيانات المنتج
        products = self.db.get_all_products()
        product_data = None
        for prod in products:
            if prod[0] == product_id:
                product_data = prod
                break

        if not product_data:
            messagebox.showerror("خطأ", "لم يتم العثور على المنتج")
            return

        dialog = tk.Toplevel(self.root)
        dialog.title("تعديل منتج")
        dialog.geometry("450x400")
        dialog.configure(bg=self.colors['white'])
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        # عنوان النافذة
        title_label = tk.Label(dialog, text="✏️ تعديل منتج",
                              font=('Segoe UI', 16, 'bold'),
                              bg=self.colors['white'],
                              fg=self.colors['warning'])
        title_label.pack(pady=20)

        # إطار المحتوى
        content_frame = tk.Frame(dialog, bg=self.colors['white'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)

        # اسم المنتج
        tk.Label(content_frame, text="📝 اسم المنتج:",
                font=('Segoe UI', 12, 'bold'),
                bg=self.colors['white'],
                fg=self.colors['dark']).pack(anchor='w', pady=(0, 5))

        name_entry = tk.Entry(content_frame, font=('Segoe UI', 11), width=35)
        name_entry.pack(fill=tk.X, pady=(0, 15))
        name_entry.insert(0, product_data[1])
        name_entry.focus()

        # السعر
        tk.Label(content_frame, text="💰 السعر (ريال):",
                font=('Segoe UI', 12, 'bold'),
                bg=self.colors['white'],
                fg=self.colors['dark']).pack(anchor='w', pady=(0, 5))

        price_entry = tk.Entry(content_frame, font=('Segoe UI', 11), width=35)
        price_entry.pack(fill=tk.X, pady=(0, 15))
        price_entry.insert(0, str(product_data[2]))

        # الكمية
        tk.Label(content_frame, text="📦 الكمية:",
                font=('Segoe UI', 12, 'bold'),
                bg=self.colors['white'],
                fg=self.colors['dark']).pack(anchor='w', pady=(0, 5))

        quantity_entry = tk.Entry(content_frame, font=('Segoe UI', 11), width=35)
        quantity_entry.pack(fill=tk.X, pady=(0, 15))
        quantity_entry.insert(0, str(product_data[3]))

        # الباركود
        tk.Label(content_frame, text="🏷️ الباركود:",
                font=('Segoe UI', 12, 'bold'),
                bg=self.colors['white'],
                fg=self.colors['dark']).pack(anchor='w', pady=(0, 5))

        barcode_entry = tk.Entry(content_frame, font=('Segoe UI', 11), width=35)
        barcode_entry.pack(fill=tk.X, pady=(0, 20))
        if product_data[4]:
            barcode_entry.insert(0, product_data[4])

        # أزرار العمليات
        buttons_frame = tk.Frame(content_frame, bg=self.colors['white'])
        buttons_frame.pack(fill=tk.X)

        def update_product():
            name = name_entry.get().strip()
            price_str = price_entry.get().strip()
            quantity_str = quantity_entry.get().strip()
            barcode = barcode_entry.get().strip()

            # التحقق من البيانات
            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم المنتج")
                return

            try:
                price = float(price_str)
                if price <= 0:
                    messagebox.showerror("خطأ", "يرجى إدخال سعر صحيح")
                    return
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال سعر صحيح")
                return

            try:
                quantity = int(quantity_str)
                if quantity < 0:
                    messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة")
                    return
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة")
                return

            # محاولة تحديث المنتج
            try:
                self.db.update_product(product_id, name, price, quantity, barcode)
                messagebox.showinfo("نجح", f"تم تحديث المنتج '{name}' بنجاح!")
                self.load_products()
                dialog.destroy()
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ في التحديث: {str(e)}")

        # زر الحفظ
        save_btn = tk.Button(buttons_frame, text="💾 حفظ التغييرات",
                           command=update_product,
                           bg=self.colors['success'],
                           fg=self.colors['white'],
                           font=('Segoe UI', 11, 'bold'),
                           relief=tk.FLAT,
                           pady=10,
                           cursor='hand2')
        save_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        # زر الإلغاء
        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء",
                             command=dialog.destroy,
                             bg=self.colors['secondary'],
                             fg=self.colors['white'],
                             font=('Segoe UI', 11, 'bold'),
                             relief=tk.FLAT,
                             pady=10,
                             cursor='hand2')
        cancel_btn.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))

        # ربط Enter للحفظ
        dialog.bind('<Return>', lambda e: update_product())

    def delete_product_dialog(self):
        """حذف منتج"""
        products = self.db.get_all_products()
        if not products:
            messagebox.showwarning("تحذير", "لا توجد منتجات للحذف")
            return

        dialog = tk.Toplevel(self.root)
        dialog.title("حذف منتج")
        dialog.geometry("400x200")
        dialog.configure(bg=self.colors['white'])
        dialog.resizable(False, False)
        dialog.transient(self.root)
        dialog.grab_set()

        tk.Label(dialog, text="🗑️ اختر المنتج المراد حذفه",
                font=('Segoe UI', 14, 'bold'),
                bg=self.colors['white'],
                fg=self.colors['danger']).pack(pady=20)

        product_var = tk.StringVar()
        product_combo = ttk.Combobox(dialog, textvariable=product_var,
                                   font=('Segoe UI', 11), width=40)
        product_combo['values'] = [f"{prod[1]} - {prod[2]:.2f} ريال (ID: {prod[0]})" for prod in products]
        product_combo.pack(pady=10)

        def delete_selected():
            if not product_var.get():
                messagebox.showwarning("تحذير", "يرجى اختيار منتج")
                return

            product_id = int(product_var.get().split("ID: ")[1].split(")")[0])
            self.delete_product_quick(product_id)
            dialog.destroy()

        buttons_frame = tk.Frame(dialog, bg=self.colors['white'])
        buttons_frame.pack(pady=20)

        tk.Button(buttons_frame, text="🗑️ حذف",
                 command=delete_selected,
                 bg=self.colors['danger'],
                 fg=self.colors['white'],
                 font=('Segoe UI', 11, 'bold'),
                 relief=tk.FLAT,
                 padx=20, pady=8,
                 cursor='hand2').pack(side=tk.LEFT, padx=(0, 10))

        tk.Button(buttons_frame, text="❌ إلغاء",
                 command=dialog.destroy,
                 bg=self.colors['secondary'],
                 fg=self.colors['white'],
                 font=('Segoe UI', 11, 'bold'),
                 relief=tk.FLAT,
                 padx=20, pady=8,
                 cursor='hand2').pack(side=tk.LEFT)

    def delete_product_quick(self, product_id):
        """حذف سريع للمنتج"""
        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذا المنتج؟"):
            try:
                self.db.delete_product(product_id)
                messagebox.showinfo("نجح", "تم حذف المنتج بنجاح!")
                self.load_products()
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل في حذف المنتج: {str(e)}")

    def search_products(self, event):
        """البحث في المنتجات"""
        search_term = self.search_var.get().strip()

        # مسح المنتجات الحالية
        for widget in self.products_scrollable_frame.winfo_children():
            widget.destroy()

        if search_term:
            # البحث في المنتجات
            all_products = self.db.get_all_products()
            filtered_products = []

            for product in all_products:
                if (search_term.lower() in product[1].lower() or  # البحث في الاسم
                    (product[4] and search_term in product[4])):  # البحث في الباركود
                    filtered_products.append(product)

            products = filtered_products
        else:
            # عرض جميع المنتجات أو منتجات الفئة المختارة
            if self.selected_category:
                products = self.db.get_products_by_category(self.selected_category)
            else:
                products = self.db.get_all_products()

        # تحديث عداد المنتجات
        self.products_count.config(text=str(len(products)))

        # عرض المنتجات (نفس كود load_products)
        row = 0
        col = 0
        max_cols = 3

        for product in products:
            if len(product) >= 8:
                product_id, name, price, quantity, barcode, category_id, created_at, category_name, category_color = product
            else:
                product_id, name, price, quantity, barcode, category_id, created_at = product

            # بطاقة المنتج العصرية (نفس التصميم)
            product_card = tk.Frame(self.products_scrollable_frame,
                                  bg=self.colors['white'],
                                  relief=tk.SOLID,
                                  bd=1,
                                  width=220,
                                  height=180)
            product_card.grid(row=row, column=col, padx=12, pady=12, sticky='nsew')
            product_card.grid_propagate(False)

            # شريط علوي ملون
            header_bar = tk.Frame(product_card, bg=self.colors['info'], height=6)
            header_bar.pack(fill=tk.X)

            # محتوى البطاقة
            content_frame = tk.Frame(product_card, bg=self.colors['white'])
            content_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=12)

            # اسم المنتج
            name_label = tk.Label(content_frame, text=name,
                                font=('Segoe UI', 12, 'bold'),
                                bg=self.colors['white'],
                                fg=self.colors['dark'],
                                wraplength=190)
            name_label.pack(anchor='w', pady=(0, 8))

            # معلومات المنتج
            info_frame = tk.Frame(content_frame, bg=self.colors['white'])
            info_frame.pack(fill=tk.X, pady=(0, 12))

            # السعر
            price_frame = tk.Frame(info_frame, bg=self.colors['white'])
            price_frame.pack(fill=tk.X, pady=(0, 4))

            tk.Label(price_frame, text="💰",
                    font=('Segoe UI', 11),
                    bg=self.colors['white']).pack(side=tk.LEFT)

            price_label = tk.Label(price_frame, text=f"{price:.2f} ريال",
                                 font=('Segoe UI', 11, 'bold'),
                                 bg=self.colors['white'],
                                 fg=self.colors['success'])
            price_label.pack(side=tk.LEFT, padx=(5, 0))

            # الكمية
            quantity_frame = tk.Frame(info_frame, bg=self.colors['white'])
            quantity_frame.pack(fill=tk.X)

            tk.Label(quantity_frame, text="📦",
                    font=('Segoe UI', 11),
                    bg=self.colors['white']).pack(side=tk.LEFT)

            quantity_color = self.colors['success'] if quantity > 10 else self.colors['warning'] if quantity > 0 else self.colors['danger']
            quantity_label = tk.Label(quantity_frame, text=f"الكمية: {quantity}",
                                    font=('Segoe UI', 10),
                                    bg=self.colors['white'],
                                    fg=quantity_color)
            quantity_label.pack(side=tk.LEFT, padx=(5, 0))

            # أزرار العمليات
            buttons_frame = tk.Frame(content_frame, bg=self.colors['white'])
            buttons_frame.pack(fill=tk.X, pady=(8, 0))

            # زر الإضافة للسلة
            if quantity > 0:
                add_btn = tk.Button(buttons_frame, text="➕ إضافة للسلة",
                                  font=('Segoe UI', 10, 'bold'),
                                  bg=self.colors['success'],
                                  fg=self.colors['white'],
                                  relief=tk.FLAT,
                                  cursor='hand2',
                                  activebackground='#059669',
                                  command=lambda p=product: self.add_to_cart(p))
                add_btn.pack(fill=tk.X, pady=(0, 6))
            else:
                tk.Label(buttons_frame, text="❌ نفد المخزون",
                        font=('Segoe UI', 10, 'bold'),
                        bg=self.colors['danger'],
                        fg=self.colors['white']).pack(fill=tk.X, pady=(0, 6))

            # أزرار صغيرة للتعديل والحذف
            small_buttons = tk.Frame(buttons_frame, bg=self.colors['white'])
            small_buttons.pack(fill=tk.X)

            # زر التعديل
            edit_btn = tk.Button(small_buttons, text="✏️ تعديل",
                               font=('Segoe UI', 9, 'bold'),
                               bg=self.colors['warning'],
                               fg=self.colors['white'],
                               relief=tk.FLAT,
                               cursor='hand2',
                               activebackground='#d97706',
                               command=lambda p_id=product_id: self.edit_product_quick(p_id))
            edit_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 3))

            # زر الحذف
            delete_btn = tk.Button(small_buttons, text="🗑️ حذف",
                                 font=('Segoe UI', 9, 'bold'),
                                 bg=self.colors['danger'],
                                 fg=self.colors['white'],
                                 relief=tk.FLAT,
                                 cursor='hand2',
                                 activebackground='#dc2626',
                                 command=lambda p_id=product_id: self.delete_product_quick(p_id))
            delete_btn.pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(3, 0))

            # الانتقال للعمود التالي
            col += 1
            if col >= max_cols:
                col = 0
                row += 1

        # تكوين الأعمدة
        for i in range(max_cols):
            self.products_scrollable_frame.grid_columnconfigure(i, weight=1)

    def add_to_cart(self, product):
        """إضافة منتج للسلة"""
        if len(product) >= 8:
            product_id, name, price, quantity, barcode, category_id, created_at, category_name, category_color = product
        else:
            product_id, name, price, quantity, barcode, category_id, created_at = product

        if quantity <= 0:
            messagebox.showwarning("تحذير", f"المنتج '{name}' غير متوفر في المخزون")
            return

        # البحث عن المنتج في السلة
        for item in self.cart:
            if item['id'] == product_id:
                if item['quantity'] < quantity:
                    item['quantity'] += 1
                    messagebox.showinfo("تم", f"تم زيادة كمية '{name}' في السلة")
                else:
                    messagebox.showwarning("تحذير", f"لا يمكن إضافة المزيد من '{name}'\nالكمية المتوفرة: {quantity}")
                self.update_cart_display()
                return

        # إضافة منتج جديد للسلة
        cart_item = {
            'id': product_id,
            'name': name,
            'price': price,
            'quantity': 1,
            'total': price
        }

        self.cart.append(cart_item)
        messagebox.showinfo("تم", f"تم إضافة '{name}' للسلة")
        self.update_cart_display()

    def update_cart_display(self):
        """تحديث عرض السلة"""
        # مسح السلة الحالية
        for widget in self.cart_scrollable_frame.winfo_children():
            widget.destroy()

        # تحديث عداد العناصر
        self.cart_count.config(text=str(len(self.cart)))

        total_amount = 0

        for i, item in enumerate(self.cart):
            item_total = item['price'] * item['quantity']
            total_amount += item_total

            # بطاقة عنصر السلة
            item_card = tk.Frame(self.cart_scrollable_frame,
                               bg=self.colors['white'],
                               relief=tk.SOLID,
                               bd=1)
            item_card.pack(fill=tk.X, pady=5, padx=5)

            # محتوى العنصر
            content_frame = tk.Frame(item_card, bg=self.colors['white'])
            content_frame.pack(fill=tk.X, padx=10, pady=8)

            # اسم المنتج
            name_label = tk.Label(content_frame, text=item['name'],
                                font=('Segoe UI', 11, 'bold'),
                                bg=self.colors['white'],
                                fg=self.colors['dark'],
                                wraplength=300)
            name_label.pack(anchor='w')

            # تفاصيل السعر والكمية
            details_frame = tk.Frame(content_frame, bg=self.colors['white'])
            details_frame.pack(fill=tk.X, pady=(5, 0))

            # السعر والكمية
            price_qty_label = tk.Label(details_frame,
                                     text=f"{item['price']:.2f} × {item['quantity']} = {item_total:.2f} ريال",
                                     font=('Segoe UI', 10),
                                     bg=self.colors['white'],
                                     fg=self.colors['success'])
            price_qty_label.pack(side=tk.LEFT)

            # أزرار التحكم
            controls_frame = tk.Frame(details_frame, bg=self.colors['white'])
            controls_frame.pack(side=tk.RIGHT)

            # زر تقليل الكمية
            minus_btn = tk.Button(controls_frame, text="➖",
                                font=('Segoe UI', 8, 'bold'),
                                bg=self.colors['warning'],
                                fg=self.colors['white'],
                                relief=tk.FLAT,
                                width=2,
                                cursor='hand2',
                                command=lambda idx=i: self.decrease_cart_item(idx))
            minus_btn.pack(side=tk.LEFT, padx=(0, 2))

            # زر زيادة الكمية
            plus_btn = tk.Button(controls_frame, text="➕",
                               font=('Segoe UI', 8, 'bold'),
                               bg=self.colors['success'],
                               fg=self.colors['white'],
                               relief=tk.FLAT,
                               width=2,
                               cursor='hand2',
                               command=lambda idx=i: self.increase_cart_item(idx))
            plus_btn.pack(side=tk.LEFT, padx=(0, 2))

            # زر حذف العنصر
            remove_btn = tk.Button(controls_frame, text="🗑️",
                                 font=('Segoe UI', 8, 'bold'),
                                 bg=self.colors['danger'],
                                 fg=self.colors['white'],
                                 relief=tk.FLAT,
                                 width=2,
                                 cursor='hand2',
                                 command=lambda idx=i: self.remove_cart_item(idx))
            remove_btn.pack(side=tk.LEFT)

        # تحديث الإجمالي
        self.total_label.config(text=f"{total_amount:.2f} ريال")

    def increase_cart_item(self, index):
        """زيادة كمية عنصر في السلة"""
        if index < len(self.cart):
            item = self.cart[index]
            # التحقق من الكمية المتوفرة
            products = self.db.get_all_products()
            available_quantity = 0
            for prod in products:
                if prod[0] == item['id']:
                    available_quantity = prod[3]
                    break

            if item['quantity'] < available_quantity:
                item['quantity'] += 1
                item['total'] = item['price'] * item['quantity']
                self.update_cart_display()
            else:
                messagebox.showwarning("تحذير", f"الكمية المتوفرة من '{item['name']}': {available_quantity}")

    def decrease_cart_item(self, index):
        """تقليل كمية عنصر في السلة"""
        if index < len(self.cart):
            item = self.cart[index]
            if item['quantity'] > 1:
                item['quantity'] -= 1
                item['total'] = item['price'] * item['quantity']
                self.update_cart_display()
            else:
                self.remove_cart_item(index)

    def remove_cart_item(self, index):
        """حذف عنصر من السلة"""
        if index < len(self.cart):
            item_name = self.cart[index]['name']
            del self.cart[index]
            messagebox.showinfo("تم", f"تم حذف '{item_name}' من السلة")
            self.update_cart_display()

    def create_invoice(self):
        """إنشاء فاتورة"""
        if not self.cart:
            messagebox.showwarning("تحذير", "السلة فارغة!")
            return

        customer_name = self.customer_name_var.get().strip()
        if not customer_name:
            customer_name = "عميل"

        try:
            # إنشاء الفاتورة
            invoice_path = self.invoice_generator.create_invoice(self.cart, customer_name)

            if invoice_path:
                # تحديث المخزون
                for item in self.cart:
                    self.db.reduce_product_quantity(item['id'], item['quantity'])

                messagebox.showinfo("نجح", f"تم إنشاء الفاتورة بنجاح!\n\nالملف: {invoice_path}\nالعميل: {customer_name}")

                # مسح السلة
                self.clear_cart()

                # تحديث عرض المنتجات
                self.load_products()

                # فتح الفاتورة
                if messagebox.askyesno("فتح الفاتورة", "هل تريد فتح الفاتورة الآن؟"):
                    os.startfile(invoice_path)
            else:
                messagebox.showerror("خطأ", "فشل في إنشاء الفاتورة")

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنشاء الفاتورة:\n{str(e)}")

    def clear_cart(self):
        """مسح السلة"""
        if self.cart:
            if messagebox.askyesno("تأكيد", "هل أنت متأكد من مسح السلة؟"):
                self.cart.clear()
                self.update_cart_display()
                messagebox.showinfo("تم", "تم مسح السلة")
        else:
            messagebox.showinfo("معلومة", "السلة فارغة بالفعل")

class SimplePOSSystem(ModernPOSSystem):
    """نظام نقطة البيع البسيط - وراثة من النظام العصري"""
    pass

def main():
    root = tk.Tk()
    app = SimplePOSSystem(root)
    root.mainloop()

if __name__ == "__main__":
    main()
