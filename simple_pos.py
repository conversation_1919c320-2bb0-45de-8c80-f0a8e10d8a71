import tkinter as tk
from tkinter import ttk, messagebox
from database import Database
from invoice_generator import InvoiceGenerator
import os

class SimplePOS:
    def __init__(self, root):
        self.root = root
        self.root.title("نظام نقطة البيع البسيط")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f5f5f5')
        
        # إنشاء قاعدة البيانات
        self.db = Database()
        self.invoice_generator = InvoiceGenerator(self.db)
        
        # سلة التسوق
        self.cart = []
        self.selected_category = None
        
        # إنشاء الواجهة
        self.setup_styles()
        self.create_widgets()
        self.load_categories()
    
    def setup_styles(self):
        """إعداد الأنماط"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # أنماط مخصصة
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), background='#f5f5f5')
        style.configure('Category.TButton', font=('Arial', 12, 'bold'), padding=10)
        style.configure('Product.TButton', font=('Arial', 10), padding=8)
        style.configure('Cart.TLabel', font=('Arial', 12, 'bold'), background='white')
        style.configure('Total.TLabel', font=('Arial', 14, 'bold'), background='white', foreground='#2E7D32')
    
    def create_widgets(self):
        """إنشاء عناصر الواجهة"""
        # الإطار الرئيسي
        main_container = tk.Frame(self.root, bg='#f5f5f5')
        main_container.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = ttk.Label(main_container, text="🛒 نظام نقطة البيع", style='Title.TLabel')
        title_label.pack(pady=(0, 20))
        
        # الإطار الأساسي (3 أعمدة)
        content_frame = tk.Frame(main_container, bg='#f5f5f5')
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # العمود الأول: الفئات
        self.create_categories_panel(content_frame)
        
        # العمود الثاني: المنتجات
        self.create_products_panel(content_frame)
        
        # العمود الثالث: السلة والفاتورة
        self.create_cart_panel(content_frame)
    
    def create_categories_panel(self, parent):
        """إنشاء لوحة الفئات"""
        categories_frame = tk.Frame(parent, bg='white', relief=tk.RAISED, bd=1)
        categories_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        
        # عنوان الفئات
        header_frame = tk.Frame(categories_frame, bg='#2196F3', height=50)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="📂 الفئات", font=('Arial', 14, 'bold'), 
                bg='#2196F3', fg='white').pack(expand=True)
        
        # أزرار إدارة الفئات
        buttons_frame = tk.Frame(categories_frame, bg='white')
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(buttons_frame, text="➕ إضافة فئة",
                  command=self.add_category_dialog).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(buttons_frame, text="🗑️ حذف فئة",
                  command=self.delete_category_dialog).pack(fill=tk.X, pady=(0, 5))
        
        # قائمة الفئات
        self.categories_frame = tk.Frame(categories_frame, bg='white')
        self.categories_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        categories_frame.configure(width=250)
    
    def create_products_panel(self, parent):
        """إنشاء لوحة المنتجات"""
        products_frame = tk.Frame(parent, bg='white', relief=tk.RAISED, bd=1)
        products_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # عنوان المنتجات
        header_frame = tk.Frame(products_frame, bg='#4CAF50', height=50)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        self.products_title = tk.Label(header_frame, text="🛍️ المنتجات", 
                                      font=('Arial', 14, 'bold'), bg='#4CAF50', fg='white')
        self.products_title.pack(expand=True)
        
        # أزرار إدارة المنتجات
        buttons_frame = tk.Frame(products_frame, bg='white')
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        ttk.Button(buttons_frame, text="➕ إضافة منتج",
                  command=self.add_product_dialog).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="🗑️ حذف منتج",
                  command=self.delete_product_dialog).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(buttons_frame, text="✏️ تعديل منتج",
                  command=self.edit_product_dialog).pack(side=tk.LEFT, padx=(0, 5))
        
        # شريط البحث
        search_frame = tk.Frame(buttons_frame, bg='white')
        search_frame.pack(side=tk.RIGHT)
        
        tk.Label(search_frame, text="🔍", bg='white', font=('Arial', 12)).pack(side=tk.LEFT)
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=self.search_var, width=20, font=('Arial', 10))
        search_entry.pack(side=tk.LEFT, padx=(5, 0))
        search_entry.bind('<KeyRelease>', self.search_products)
        
        # منطقة المنتجات (قابلة للتمرير)
        canvas_frame = tk.Frame(products_frame, bg='white')
        canvas_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=(0, 10))
        
        self.products_canvas = tk.Canvas(canvas_frame, bg='white')
        scrollbar = ttk.Scrollbar(canvas_frame, orient="vertical", command=self.products_canvas.yview)
        self.products_scrollable_frame = tk.Frame(self.products_canvas, bg='white')
        
        self.products_scrollable_frame.bind(
            "<Configure>",
            lambda e: self.products_canvas.configure(scrollregion=self.products_canvas.bbox("all"))
        )
        
        self.products_canvas.create_window((0, 0), window=self.products_scrollable_frame, anchor="nw")
        self.products_canvas.configure(yscrollcommand=scrollbar.set)
        
        self.products_canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
        
        # ربط عجلة الماوس
        self.products_canvas.bind("<MouseWheel>", self._on_mousewheel)
    
    def create_cart_panel(self, parent):
        """إنشاء لوحة السلة"""
        cart_frame = tk.Frame(parent, bg='white', relief=tk.RAISED, bd=1)
        cart_frame.pack(side=tk.RIGHT, fill=tk.Y)
        
        # عنوان السلة
        header_frame = tk.Frame(cart_frame, bg='#FF9800', height=50)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)
        
        tk.Label(header_frame, text="🛒 سلة التسوق", font=('Arial', 14, 'bold'), 
                bg='#FF9800', fg='white').pack(expand=True)
        
        # قائمة السلة
        self.cart_listbox = tk.Listbox(cart_frame, font=('Arial', 10), height=15, width=35)
        self.cart_listbox.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # أزرار السلة
        cart_buttons = tk.Frame(cart_frame, bg='white')
        cart_buttons.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        ttk.Button(cart_buttons, text="🗑️ حذف", command=self.remove_from_cart).pack(fill=tk.X, pady=(0, 5))
        ttk.Button(cart_buttons, text="🧹 مسح الكل", command=self.clear_cart).pack(fill=tk.X, pady=(0, 10))
        
        # معلومات العميل
        customer_frame = tk.LabelFrame(cart_frame, text="معلومات العميل", bg='white', font=('Arial', 10, 'bold'))
        customer_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        tk.Label(customer_frame, text="الاسم:", bg='white').grid(row=0, column=0, sticky='w', padx=5, pady=2)
        self.customer_name = tk.StringVar()
        tk.Entry(customer_frame, textvariable=self.customer_name, width=25).grid(row=0, column=1, padx=5, pady=2)
        
        # ملخص الفاتورة
        summary_frame = tk.Frame(cart_frame, bg='#E8F5E8', relief=tk.RAISED, bd=1)
        summary_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        self.subtotal_label = tk.Label(summary_frame, text="المجموع: 0.00 ريال", 
                                      font=('Arial', 11), bg='#E8F5E8')
        self.subtotal_label.pack(pady=5)
        
        self.vat_label = tk.Label(summary_frame, text="الضريبة: 0.00 ريال", 
                                 font=('Arial', 11), bg='#E8F5E8')
        self.vat_label.pack(pady=2)
        
        self.total_label = tk.Label(summary_frame, text="الإجمالي: 0.00 ريال", 
                                   font=('Arial', 12, 'bold'), bg='#E8F5E8', fg='#2E7D32')
        self.total_label.pack(pady=5)
        
        # زر الدفع
        ttk.Button(cart_frame, text="💳 إنشاء فاتورة", command=self.create_invoice,
                  style='Category.TButton').pack(fill=tk.X, padx=10, pady=(0, 10))
        
        cart_frame.configure(width=350)
    
    def _on_mousewheel(self, event):
        """التعامل مع عجلة الماوس"""
        self.products_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
    
    def load_categories(self):
        """تحميل الفئات"""
        # مسح الفئات الحالية
        for widget in self.categories_frame.winfo_children():
            widget.destroy()

        categories = self.db.get_all_categories()

        for category in categories:
            cat_id, name, description, color, created_at = category

            # إطار للفئة مع زر الحذف
            cat_frame = tk.Frame(self.categories_frame, bg='white')
            cat_frame.pack(fill=tk.X, pady=2)

            # زر الفئة الرئيسي
            btn = tk.Button(cat_frame, text=name,
                           bg=color, fg='white', font=('Arial', 11, 'bold'),
                           relief=tk.FLAT, pady=10, cursor='hand2',
                           command=lambda c_id=cat_id, c_name=name: self.select_category(c_id, c_name))
            btn.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

            # زر حذف صغير
            delete_btn = tk.Button(cat_frame, text="❌",
                                  bg='#f44336', fg='white', font=('Arial', 8),
                                  relief=tk.FLAT, width=3, cursor='hand2',
                                  command=lambda c_id=cat_id, c_name=name: self.confirm_delete_category(c_id, c_name))
            delete_btn.pack(side=tk.RIGHT, padx=(2, 0))

            # تأثير hover للزر الرئيسي
            btn.bind("<Enter>", lambda e, b=btn: b.configure(relief=tk.RAISED))
            btn.bind("<Leave>", lambda e, b=btn: b.configure(relief=tk.FLAT))

            # تأثير hover لزر الحذف
            delete_btn.bind("<Enter>", lambda e, b=delete_btn: b.configure(bg='#d32f2f'))
            delete_btn.bind("<Leave>", lambda e, b=delete_btn: b.configure(bg='#f44336'))
    
    def select_category(self, category_id, category_name):
        """اختيار فئة وعرض منتجاتها"""
        self.selected_category = category_id
        self.products_title.configure(text=f"🛍️ {category_name}")
        self.load_products()
    
    def load_products(self):
        """تحميل المنتجات"""
        # مسح المنتجات الحالية
        for widget in self.products_scrollable_frame.winfo_children():
            widget.destroy()
        
        if self.selected_category:
            products = self.db.get_products_by_category(self.selected_category)
        else:
            products = self.db.get_all_products()
        
        # عرض المنتجات في شبكة
        row = 0
        col = 0
        max_cols = 3
        
        for product in products:
            if len(product) >= 8:  # التأكد من وجود جميع الحقول
                product_id, name, price, quantity, barcode, category_id, created_at, category_name, category_color = product
            else:
                product_id, name, price, quantity, barcode, category_id, created_at = product
                category_name = "غير محدد"
                category_color = "#4CAF50"
            
            # إطار المنتج
            product_frame = tk.Frame(self.products_scrollable_frame, bg='#f9f9f9', 
                                   relief=tk.RAISED, bd=1, padx=10, pady=10)
            product_frame.grid(row=row, column=col, padx=5, pady=5, sticky='ew')
            
            # اسم المنتج
            name_label = tk.Label(product_frame, text=name, font=('Arial', 11, 'bold'), 
                                 bg='#f9f9f9', wraplength=150)
            name_label.pack()
            
            # السعر
            price_label = tk.Label(product_frame, text=f"{price:.2f} ريال", 
                                  font=('Arial', 10), bg='#f9f9f9', fg='#2E7D32')
            price_label.pack()
            
            # الكمية المتوفرة
            qty_label = tk.Label(product_frame, text=f"متوفر: {quantity}", 
                                font=('Arial', 9), bg='#f9f9f9', fg='#666')
            qty_label.pack()
            
            # أزرار العمليات
            buttons_frame = tk.Frame(product_frame, bg='#f9f9f9')
            buttons_frame.pack(pady=(5, 0))

            # زر الإضافة
            if quantity > 0:
                add_btn = tk.Button(buttons_frame, text="➕",
                                   bg='#4CAF50', fg='white', font=('Arial', 9, 'bold'),
                                   relief=tk.FLAT, cursor='hand2', width=3,
                                   command=lambda p=product: self.add_to_cart(p))
                add_btn.pack(side=tk.LEFT, padx=(0, 2))

                # تأثير hover
                add_btn.bind("<Enter>", lambda e, b=add_btn: b.configure(bg='#45a049'))
                add_btn.bind("<Leave>", lambda e, b=add_btn: b.configure(bg='#4CAF50'))
            else:
                tk.Label(buttons_frame, text="نفد", font=('Arial', 8),
                        bg='#f9f9f9', fg='red', width=3).pack(side=tk.LEFT, padx=(0, 2))

            # زر التعديل
            edit_btn = tk.Button(buttons_frame, text="✏️",
                               bg='#FF9800', fg='white', font=('Arial', 9),
                               relief=tk.FLAT, cursor='hand2', width=3,
                               command=lambda p=product: self.edit_product_from_card(p))
            edit_btn.pack(side=tk.LEFT, padx=(0, 2))

            # زر الحذف
            delete_btn = tk.Button(buttons_frame, text="🗑️",
                                 bg='#f44336', fg='white', font=('Arial', 9),
                                 relief=tk.FLAT, cursor='hand2', width=3,
                                 command=lambda p=product: self.confirm_delete_product(p))
            delete_btn.pack(side=tk.LEFT)

            # تأثيرات hover
            edit_btn.bind("<Enter>", lambda e, b=edit_btn: b.configure(bg='#F57C00'))
            edit_btn.bind("<Leave>", lambda e, b=edit_btn: b.configure(bg='#FF9800'))

            delete_btn.bind("<Enter>", lambda e, b=delete_btn: b.configure(bg='#d32f2f'))
            delete_btn.bind("<Leave>", lambda e, b=delete_btn: b.configure(bg='#f44336'))
            
            col += 1
            if col >= max_cols:
                col = 0
                row += 1
        
        # تحديث منطقة التمرير
        self.products_scrollable_frame.update_idletasks()
        self.products_canvas.configure(scrollregion=self.products_canvas.bbox("all"))
    
    def search_products(self, event=None):
        """البحث في المنتجات"""
        search_term = self.search_var.get().strip()
        
        # مسح المنتجات الحالية
        for widget in self.products_scrollable_frame.winfo_children():
            widget.destroy()
        
        if search_term:
            products = self.db.search_product(search_term)
            self.products_title.configure(text=f"🔍 نتائج البحث: {search_term}")
        else:
            if self.selected_category:
                products = self.db.get_products_by_category(self.selected_category)
                category = self.db.get_category_by_id(self.selected_category)
                if category:
                    self.products_title.configure(text=f"🛍️ {category[1]}")
            else:
                products = self.db.get_all_products()
                self.products_title.configure(text="🛍️ جميع المنتجات")
        
        # عرض النتائج
        self._display_products(products)

    def _display_products(self, products):
        """عرض قائمة المنتجات"""
        row = 0
        col = 0
        max_cols = 3

        for product in products:
            if len(product) >= 8:
                product_id, name, price, quantity, barcode, category_id, created_at, category_name, category_color = product
            else:
                product_id, name, price, quantity, barcode, category_id, created_at = product
                category_name = "غير محدد"
                category_color = "#4CAF50"

            # إطار المنتج
            product_frame = tk.Frame(self.products_scrollable_frame, bg='#f9f9f9',
                                   relief=tk.RAISED, bd=1, padx=10, pady=10)
            product_frame.grid(row=row, column=col, padx=5, pady=5, sticky='ew')

            # اسم المنتج
            name_label = tk.Label(product_frame, text=name, font=('Arial', 11, 'bold'),
                                 bg='#f9f9f9', wraplength=150)
            name_label.pack()

            # السعر
            price_label = tk.Label(product_frame, text=f"{price:.2f} ريال",
                                  font=('Arial', 10), bg='#f9f9f9', fg='#2E7D32')
            price_label.pack()

            # الكمية المتوفرة
            qty_label = tk.Label(product_frame, text=f"متوفر: {quantity}",
                                font=('Arial', 9), bg='#f9f9f9', fg='#666')
            qty_label.pack()

            # زر الإضافة
            if quantity > 0:
                add_btn = tk.Button(product_frame, text="➕ إضافة",
                                   bg='#4CAF50', fg='white', font=('Arial', 9, 'bold'),
                                   relief=tk.FLAT, cursor='hand2',
                                   command=lambda p=product: self.add_to_cart(p))
                add_btn.pack(pady=(5, 0))

                # تأثير hover
                add_btn.bind("<Enter>", lambda e, b=add_btn: b.configure(bg='#45a049'))
                add_btn.bind("<Leave>", lambda e, b=add_btn: b.configure(bg='#4CAF50'))
            else:
                tk.Label(product_frame, text="نفد المخزون", font=('Arial', 9),
                        bg='#f9f9f9', fg='red').pack(pady=(5, 0))

            col += 1
            if col >= max_cols:
                col = 0
                row += 1

        # تحديث منطقة التمرير
        self.products_scrollable_frame.update_idletasks()
        self.products_canvas.configure(scrollregion=self.products_canvas.bbox("all"))

    def add_to_cart(self, product):
        """إضافة منتج للسلة"""
        if len(product) >= 8:
            product_id, name, price, quantity, barcode, category_id, created_at, category_name, category_color = product
        else:
            product_id, name, price, quantity, barcode, category_id, created_at = product

        # التحقق من توفر المنتج
        if quantity <= 0:
            messagebox.showwarning("تحذير", "هذا المنتج غير متوفر في المخزون")
            return

        # البحث عن المنتج في السلة
        for cart_item in self.cart:
            if cart_item['product_id'] == product_id:
                if cart_item['quantity'] < quantity:
                    cart_item['quantity'] += 1
                    self.update_cart_display()
                    return
                else:
                    messagebox.showwarning("تحذير", "لا يمكن إضافة كمية أكثر من المتوفر")
                    return

        # إضافة منتج جديد للسلة
        cart_item = {
            'product_id': product_id,
            'product_name': name,
            'unit_price': price,
            'quantity': 1
        }
        self.cart.append(cart_item)
        self.update_cart_display()

    def update_cart_display(self):
        """تحديث عرض السلة"""
        self.cart_listbox.delete(0, tk.END)

        subtotal = 0
        for i, cart_item in enumerate(self.cart):
            total_price = cart_item['quantity'] * cart_item['unit_price']
            subtotal += total_price

            item_text = f"{cart_item['product_name']}\n{cart_item['quantity']} × {cart_item['unit_price']:.2f} = {total_price:.2f} ريال"
            self.cart_listbox.insert(tk.END, item_text)

        # تحديث ملخص الفاتورة
        vat_amount = subtotal * 0.15
        total_amount = subtotal + vat_amount

        self.subtotal_label.config(text=f"المجموع: {subtotal:.2f} ريال")
        self.vat_label.config(text=f"الضريبة (15%): {vat_amount:.2f} ريال")
        self.total_label.config(text=f"الإجمالي: {total_amount:.2f} ريال")

    def remove_from_cart(self):
        """حذف منتج من السلة"""
        selection = self.cart_listbox.curselection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج لحذفه")
            return

        index = selection[0]
        del self.cart[index]
        self.update_cart_display()

    def clear_cart(self):
        """مسح السلة"""
        if self.cart and messagebox.askyesno("تأكيد", "هل تريد مسح جميع المنتجات من السلة؟"):
            self.cart.clear()
            self.update_cart_display()

    def create_invoice(self):
        """إنشاء فاتورة جديدة"""
        if not self.cart:
            messagebox.showwarning("تحذير", "السلة فارغة")
            return

        try:
            # إنشاء الفاتورة في قاعدة البيانات
            invoice_id, invoice_number = self.db.create_invoice(
                self.customer_name.get() or "عميل نقدي",
                "",  # الرقم الضريبي
                self.cart
            )

            # إنشاء ملف PDF
            pdf_filename = self.invoice_generator.generate_invoice_pdf(invoice_id)

            messagebox.showinfo("نجح", f"تم إنشاء الفاتورة بنجاح!\nرقم الفاتورة: {invoice_number}")

            # مسح السلة وإعادة تحميل المنتجات
            self.cart.clear()
            self.update_cart_display()
            self.customer_name.set("")
            self.load_products()

            # فتح الفاتورة
            try:
                os.startfile(pdf_filename)
            except:
                pass

        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء إنشاء الفاتورة: {str(e)}")

    def add_category_dialog(self):
        """نافذة إضافة فئة جديدة"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة فئة جديدة")
        dialog.geometry("450x350")
        dialog.resizable(False, False)
        dialog.configure(bg='white')

        # جعل النافذة في المقدمة
        dialog.transient(self.root)
        dialog.grab_set()

        # العنوان
        tk.Label(dialog, text="📂 إضافة فئة جديدة", font=('Arial', 14, 'bold'),
                bg='white').pack(pady=20)

        # الحقول
        fields_frame = tk.Frame(dialog, bg='white')
        fields_frame.pack(padx=20, pady=10)

        tk.Label(fields_frame, text="اسم الفئة:", font=('Arial', 11), bg='white').grid(row=0, column=0, sticky='w', pady=5)
        name_var = tk.StringVar()
        tk.Entry(fields_frame, textvariable=name_var, width=25, font=('Arial', 11)).grid(row=0, column=1, padx=10, pady=5)

        tk.Label(fields_frame, text="الوصف:", font=('Arial', 11), bg='white').grid(row=1, column=0, sticky='w', pady=5)
        desc_var = tk.StringVar()
        tk.Entry(fields_frame, textvariable=desc_var, width=25, font=('Arial', 11)).grid(row=1, column=1, padx=10, pady=5)

        tk.Label(fields_frame, text="اللون:", font=('Arial', 11), bg='white').grid(row=2, column=0, sticky='w', pady=5)
        color_var = tk.StringVar(value="#4CAF50")

        color_frame = tk.Frame(fields_frame, bg='white')
        color_frame.grid(row=2, column=1, padx=10, pady=5, sticky='w')

        colors = [("#4CAF50", "أخضر"), ("#2196F3", "أزرق"), ("#FF9800", "برتقالي"),
                 ("#E91E63", "وردي"), ("#9C27B0", "بنفسجي"), ("#00BCD4", "سماوي")]

        for i, (color_code, color_name) in enumerate(colors):
            rb = tk.Radiobutton(color_frame, text=color_name, variable=color_var, value=color_code,
                               bg='white', font=('Arial', 9))
            rb.grid(row=i//3, column=i%3, sticky='w', padx=5)

        def save_category():
            name = name_var.get().strip()
            description = desc_var.get().strip()
            color = color_var.get()

            if not name:
                messagebox.showwarning("تحذير", "يرجى إدخال اسم الفئة")
                return

            result = self.db.add_category(name, description, color)
            if result is not None:
                messagebox.showinfo("نجح", "تم إضافة الفئة بنجاح")
                self.load_categories()
                dialog.destroy()
            else:
                messagebox.showerror("خطأ", "فشل في إضافة الفئة (ربما الاسم مكرر)")

        # مساحة إضافية
        tk.Frame(dialog, bg='white', height=20).pack()

        # الأزرار
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(pady=20)

        save_btn = tk.Button(buttons_frame, text="💾 حفظ", command=save_category,
                            bg='#4CAF50', fg='white', font=('Arial', 12, 'bold'),
                            padx=25, pady=8, relief=tk.FLAT, cursor='hand2')
        save_btn.pack(side=tk.LEFT, padx=10)

        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء", command=dialog.destroy,
                              bg='#f44336', fg='white', font=('Arial', 12, 'bold'),
                              padx=25, pady=8, relief=tk.FLAT, cursor='hand2')
        cancel_btn.pack(side=tk.LEFT, padx=10)

        # تأثيرات hover
        save_btn.bind("<Enter>", lambda e: save_btn.configure(bg='#45a049'))
        save_btn.bind("<Leave>", lambda e: save_btn.configure(bg='#4CAF50'))

        cancel_btn.bind("<Enter>", lambda e: cancel_btn.configure(bg='#d32f2f'))
        cancel_btn.bind("<Leave>", lambda e: cancel_btn.configure(bg='#f44336'))

    def add_product_dialog(self):
        """نافذة إضافة منتج جديد"""
        if not self.selected_category:
            messagebox.showwarning("تحذير", "يرجى اختيار فئة أولاً")
            return

        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة منتج جديد")
        dialog.geometry("450x350")
        dialog.resizable(False, False)
        dialog.configure(bg='white')

        # جعل النافذة في المقدمة
        dialog.transient(self.root)
        dialog.grab_set()

        # العنوان
        category = self.db.get_category_by_id(self.selected_category)
        category_name = category[1] if category else "غير محدد"

        tk.Label(dialog, text=f"🛍️ إضافة منتج جديد", font=('Arial', 14, 'bold'),
                bg='white').pack(pady=15)

        tk.Label(dialog, text=f"الفئة: {category_name}", font=('Arial', 11),
                bg='white', fg='#666').pack()

        # الحقول
        fields_frame = tk.Frame(dialog, bg='white')
        fields_frame.pack(padx=30, pady=20)

        # اسم المنتج
        tk.Label(fields_frame, text="اسم المنتج:", font=('Arial', 11, 'bold'), bg='white').grid(row=0, column=0, sticky='w', pady=8)
        name_var = tk.StringVar()
        name_entry = tk.Entry(fields_frame, textvariable=name_var, width=25, font=('Arial', 11))
        name_entry.grid(row=0, column=1, padx=15, pady=8)
        name_entry.focus()

        # السعر
        tk.Label(fields_frame, text="السعر (ريال):", font=('Arial', 11, 'bold'), bg='white').grid(row=1, column=0, sticky='w', pady=8)
        price_var = tk.StringVar()
        tk.Entry(fields_frame, textvariable=price_var, width=25, font=('Arial', 11)).grid(row=1, column=1, padx=15, pady=8)

        # الكمية
        tk.Label(fields_frame, text="الكمية:", font=('Arial', 11, 'bold'), bg='white').grid(row=2, column=0, sticky='w', pady=8)
        quantity_var = tk.StringVar(value="1")
        tk.Entry(fields_frame, textvariable=quantity_var, width=25, font=('Arial', 11)).grid(row=2, column=1, padx=15, pady=8)

        # الباركود (اختياري)
        tk.Label(fields_frame, text="الباركود (اختياري):", font=('Arial', 11), bg='white').grid(row=3, column=0, sticky='w', pady=8)
        barcode_var = tk.StringVar()
        tk.Entry(fields_frame, textvariable=barcode_var, width=25, font=('Arial', 11)).grid(row=3, column=1, padx=15, pady=8)

        def save_product():
            try:
                name = name_var.get().strip()
                price = float(price_var.get())
                quantity = int(quantity_var.get())
                barcode = barcode_var.get().strip() or None

                if not name:
                    messagebox.showwarning("تحذير", "يرجى إدخال اسم المنتج")
                    return

                if price <= 0:
                    messagebox.showwarning("تحذير", "يرجى إدخال سعر صحيح")
                    return

                if quantity < 0:
                    messagebox.showwarning("تحذير", "يرجى إدخال كمية صحيحة")
                    return

                result = self.db.add_product(name, price, quantity, self.selected_category, barcode)
                if result is not None:
                    messagebox.showinfo("نجح", "تم إضافة المنتج بنجاح")
                    self.load_products()
                    dialog.destroy()
                else:
                    messagebox.showerror("خطأ", "فشل في إضافة المنتج (ربما الباركود مكرر)")

            except ValueError:
                messagebox.showerror("خطأ", "يرجى التأكد من صحة البيانات المدخلة")

        # مساحة إضافية
        tk.Frame(dialog, bg='white', height=15).pack()

        # الأزرار
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(pady=20)

        save_btn = tk.Button(buttons_frame, text="💾 حفظ المنتج", command=save_product,
                            bg='#4CAF50', fg='white', font=('Arial', 12, 'bold'),
                            padx=25, pady=10, relief=tk.FLAT, cursor='hand2')
        save_btn.pack(side=tk.LEFT, padx=10)

        cancel_btn = tk.Button(buttons_frame, text="❌ إلغاء", command=dialog.destroy,
                              bg='#f44336', fg='white', font=('Arial', 12, 'bold'),
                              padx=25, pady=10, relief=tk.FLAT, cursor='hand2')
        cancel_btn.pack(side=tk.LEFT, padx=10)

        # تأثيرات hover
        save_btn.bind("<Enter>", lambda e: save_btn.configure(bg='#45a049'))
        save_btn.bind("<Leave>", lambda e: save_btn.configure(bg='#4CAF50'))

        cancel_btn.bind("<Enter>", lambda e: cancel_btn.configure(bg='#d32f2f'))
        cancel_btn.bind("<Leave>", lambda e: cancel_btn.configure(bg='#f44336'))

    def confirm_delete_category(self, category_id, category_name):
        """تأكيد حذف فئة"""
        if messagebox.askyesno("تأكيد الحذف",
                              f"هل تريد حذف الفئة '{category_name}'؟\n\nملاحظة: لا يمكن حذف الفئة إذا كانت تحتوي على منتجات"):
            success, message = self.db.delete_category(category_id)

            if success:
                messagebox.showinfo("نجح", message)
                self.load_categories()
                # إذا كانت الفئة المحذوفة هي المختارة، مسح المنتجات
                if self.selected_category == category_id:
                    self.selected_category = None
                    self.products_title.configure(text="🛍️ المنتجات")
                    self.load_products()
            else:
                messagebox.showerror("خطأ", message)

    def delete_category_dialog(self):
        """نافذة حذف فئة"""
        categories = self.db.get_all_categories()
        if not categories:
            messagebox.showwarning("تحذير", "لا توجد فئات للحذف")
            return

        dialog = tk.Toplevel(self.root)
        dialog.title("حذف فئة")
        dialog.geometry("400x300")
        dialog.resizable(False, False)
        dialog.configure(bg='white')

        dialog.transient(self.root)
        dialog.grab_set()

        tk.Label(dialog, text="🗑️ حذف فئة", font=('Arial', 14, 'bold'),
                bg='white').pack(pady=20)

        tk.Label(dialog, text="اختر الفئة المراد حذفها:", font=('Arial', 11),
                bg='white').pack(pady=10)

        # قائمة الفئات
        listbox_frame = tk.Frame(dialog, bg='white')
        listbox_frame.pack(padx=20, pady=10, fill=tk.BOTH, expand=True)

        listbox = tk.Listbox(listbox_frame, font=('Arial', 11), height=8)
        scrollbar = ttk.Scrollbar(listbox_frame, orient="vertical", command=listbox.yview)
        listbox.configure(yscrollcommand=scrollbar.set)

        for category in categories:
            cat_id, name, description, color, created_at = category
            # عد المنتجات في كل فئة
            products = self.db.get_products_by_category(cat_id)
            product_count = len(products)
            listbox.insert(tk.END, f"{name} ({product_count} منتج)")

        listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        def delete_selected():
            selection = listbox.curselection()
            if not selection:
                messagebox.showwarning("تحذير", "يرجى اختيار فئة للحذف")
                return

            index = selection[0]
            category = categories[index]
            cat_id, name = category[0], category[1]

            dialog.destroy()
            self.confirm_delete_category(cat_id, name)

        # الأزرار
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(pady=20)

        tk.Button(buttons_frame, text="🗑️ حذف", command=delete_selected,
                 bg='#f44336', fg='white', font=('Arial', 11, 'bold'),
                 padx=20, pady=5, relief=tk.FLAT, cursor='hand2').pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="❌ إلغاء", command=dialog.destroy,
                 bg='#666', fg='white', font=('Arial', 11, 'bold'),
                 padx=20, pady=5, relief=tk.FLAT, cursor='hand2').pack(side=tk.LEFT, padx=5)

    def confirm_delete_product(self, product):
        """تأكيد حذف منتج"""
        if len(product) >= 8:
            product_id, name = product[0], product[1]
        else:
            product_id, name = product[0], product[1]

        if messagebox.askyesno("تأكيد الحذف",
                              f"هل تريد حذف المنتج '{name}'؟\n\nملاحظة: لا يمكن حذف المنتج إذا كان مستخدماً في فواتير"):
            success, message = self.db.delete_product(product_id)

            if success:
                messagebox.showinfo("نجح", message)
                self.load_products()
            else:
                messagebox.showerror("خطأ", message)

    def delete_product_dialog(self):
        """نافذة حذف منتج"""
        if self.selected_category:
            products = self.db.get_products_by_category(self.selected_category)
        else:
            products = self.db.get_all_products()

        if not products:
            messagebox.showwarning("تحذير", "لا توجد منتجات للحذف")
            return

        dialog = tk.Toplevel(self.root)
        dialog.title("حذف منتج")
        dialog.geometry("500x400")
        dialog.resizable(False, False)
        dialog.configure(bg='white')

        dialog.transient(self.root)
        dialog.grab_set()

        tk.Label(dialog, text="🗑️ حذف منتج", font=('Arial', 14, 'bold'),
                bg='white').pack(pady=20)

        tk.Label(dialog, text="اختر المنتج المراد حذفه:", font=('Arial', 11),
                bg='white').pack(pady=10)

        # قائمة المنتجات
        listbox_frame = tk.Frame(dialog, bg='white')
        listbox_frame.pack(padx=20, pady=10, fill=tk.BOTH, expand=True)

        listbox = tk.Listbox(listbox_frame, font=('Arial', 11), height=12)
        scrollbar = ttk.Scrollbar(listbox_frame, orient="vertical", command=listbox.yview)
        listbox.configure(yscrollcommand=scrollbar.set)

        for product in products:
            if len(product) >= 8:
                product_id, name, price, quantity = product[0], product[1], product[2], product[3]
            else:
                product_id, name, price, quantity = product[0], product[1], product[2], product[3]

            listbox.insert(tk.END, f"{name} - {price:.2f} ريال (متوفر: {quantity})")

        listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        def delete_selected():
            selection = listbox.curselection()
            if not selection:
                messagebox.showwarning("تحذير", "يرجى اختيار منتج للحذف")
                return

            index = selection[0]
            product = products[index]

            dialog.destroy()
            self.confirm_delete_product(product)

        # الأزرار
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(pady=20)

        tk.Button(buttons_frame, text="🗑️ حذف", command=delete_selected,
                 bg='#f44336', fg='white', font=('Arial', 11, 'bold'),
                 padx=20, pady=5, relief=tk.FLAT, cursor='hand2').pack(side=tk.LEFT, padx=5)

        tk.Button(buttons_frame, text="❌ إلغاء", command=dialog.destroy,
                 bg='#666', fg='white', font=('Arial', 11, 'bold'),
                 padx=20, pady=5, relief=tk.FLAT, cursor='hand2').pack(side=tk.LEFT, padx=5)

    def edit_product_from_card(self, product):
        """تعديل منتج من البطاقة"""
        self.edit_product_dialog(product)

    def edit_product_dialog(self, product=None):
        """نافذة تعديل منتج"""
        if product is None:
            # اختيار منتج للتعديل
            if self.selected_category:
                products = self.db.get_products_by_category(self.selected_category)
            else:
                products = self.db.get_all_products()

            if not products:
                messagebox.showwarning("تحذير", "لا توجد منتجات للتعديل")
                return

            # نافذة اختيار المنتج
            select_dialog = tk.Toplevel(self.root)
            select_dialog.title("اختيار منتج للتعديل")
            select_dialog.geometry("500x400")
            select_dialog.configure(bg='white')
            select_dialog.transient(self.root)
            select_dialog.grab_set()

            tk.Label(select_dialog, text="✏️ اختر منتج للتعديل", font=('Arial', 14, 'bold'),
                    bg='white').pack(pady=20)

            listbox_frame = tk.Frame(select_dialog, bg='white')
            listbox_frame.pack(padx=20, pady=10, fill=tk.BOTH, expand=True)

            listbox = tk.Listbox(listbox_frame, font=('Arial', 11), height=12)
            scrollbar = ttk.Scrollbar(listbox_frame, orient="vertical", command=listbox.yview)
            listbox.configure(yscrollcommand=scrollbar.set)

            for prod in products:
                if len(prod) >= 8:
                    name, price, quantity = prod[1], prod[2], prod[3]
                else:
                    name, price, quantity = prod[1], prod[2], prod[3]
                listbox.insert(tk.END, f"{name} - {price:.2f} ريال (متوفر: {quantity})")

            listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            def edit_selected():
                selection = listbox.curselection()
                if not selection:
                    messagebox.showwarning("تحذير", "يرجى اختيار منتج للتعديل")
                    return

                index = selection[0]
                selected_product = products[index]
                select_dialog.destroy()
                self.edit_product_dialog(selected_product)

            buttons_frame = tk.Frame(select_dialog, bg='white')
            buttons_frame.pack(pady=20)

            tk.Button(buttons_frame, text="✏️ تعديل", command=edit_selected,
                     bg='#FF9800', fg='white', font=('Arial', 11, 'bold'),
                     padx=20, pady=5, relief=tk.FLAT, cursor='hand2').pack(side=tk.LEFT, padx=5)

            tk.Button(buttons_frame, text="❌ إلغاء", command=select_dialog.destroy,
                     bg='#666', fg='white', font=('Arial', 11, 'bold'),
                     padx=20, pady=5, relief=tk.FLAT, cursor='hand2').pack(side=tk.LEFT, padx=5)

            return

        # نافذة التعديل الفعلية
        if len(product) >= 8:
            product_id, name, price, quantity, barcode, category_id = product[0], product[1], product[2], product[3], product[4], product[5]
        else:
            product_id, name, price, quantity, barcode, category_id = product[0], product[1], product[2], product[3], product[4], product[5]

        dialog = tk.Toplevel(self.root)
        dialog.title("تعديل منتج")
        dialog.geometry("450x400")
        dialog.resizable(False, False)
        dialog.configure(bg='white')

        dialog.transient(self.root)
        dialog.grab_set()

        tk.Label(dialog, text="✏️ تعديل منتج", font=('Arial', 14, 'bold'),
                bg='white').pack(pady=15)

        # الحقول
        fields_frame = tk.Frame(dialog, bg='white')
        fields_frame.pack(padx=30, pady=20)

        # اسم المنتج
        tk.Label(fields_frame, text="اسم المنتج:", font=('Arial', 11, 'bold'), bg='white').grid(row=0, column=0, sticky='w', pady=8)
        name_var = tk.StringVar(value=name)
        name_entry = tk.Entry(fields_frame, textvariable=name_var, width=25, font=('Arial', 11))
        name_entry.grid(row=0, column=1, padx=15, pady=8)
        name_entry.focus()

        # السعر
        tk.Label(fields_frame, text="السعر (ريال):", font=('Arial', 11, 'bold'), bg='white').grid(row=1, column=0, sticky='w', pady=8)
        price_var = tk.StringVar(value=str(price))
        tk.Entry(fields_frame, textvariable=price_var, width=25, font=('Arial', 11)).grid(row=1, column=1, padx=15, pady=8)

        # الكمية
        tk.Label(fields_frame, text="الكمية:", font=('Arial', 11, 'bold'), bg='white').grid(row=2, column=0, sticky='w', pady=8)
        quantity_var = tk.StringVar(value=str(quantity))
        tk.Entry(fields_frame, textvariable=quantity_var, width=25, font=('Arial', 11)).grid(row=2, column=1, padx=15, pady=8)

        # الباركود
        tk.Label(fields_frame, text="الباركود:", font=('Arial', 11), bg='white').grid(row=3, column=0, sticky='w', pady=8)
        barcode_var = tk.StringVar(value=barcode or "")
        tk.Entry(fields_frame, textvariable=barcode_var, width=25, font=('Arial', 11)).grid(row=3, column=1, padx=15, pady=8)

        def save_changes():
            try:
                new_name = name_var.get().strip()
                new_price = float(price_var.get())
                new_quantity = int(quantity_var.get())
                new_barcode = barcode_var.get().strip() or None

                if not new_name:
                    messagebox.showwarning("تحذير", "يرجى إدخال اسم المنتج")
                    return

                if new_price <= 0:
                    messagebox.showwarning("تحذير", "يرجى إدخال سعر صحيح")
                    return

                if new_quantity < 0:
                    messagebox.showwarning("تحذير", "يرجى إدخال كمية صحيحة")
                    return

                success, message = self.db.update_product(product_id, new_name, new_price, new_quantity, new_barcode)

                if success:
                    messagebox.showinfo("نجح", message)
                    self.load_products()
                    dialog.destroy()
                else:
                    messagebox.showerror("خطأ", message)

            except ValueError:
                messagebox.showerror("خطأ", "يرجى التأكد من صحة البيانات المدخلة")

        # الأزرار
        buttons_frame = tk.Frame(dialog, bg='white')
        buttons_frame.pack(pady=20)

        tk.Button(buttons_frame, text="💾 حفظ التغييرات", command=save_changes,
                 bg='#4CAF50', fg='white', font=('Arial', 12, 'bold'),
                 padx=25, pady=8, relief=tk.FLAT, cursor='hand2').pack(side=tk.LEFT, padx=10)

        tk.Button(buttons_frame, text="❌ إلغاء", command=dialog.destroy,
                 bg='#f44336', fg='white', font=('Arial', 12, 'bold'),
                 padx=25, pady=8, relief=tk.FLAT, cursor='hand2').pack(side=tk.LEFT, padx=10)

def main():
    root = tk.Tk()
    app = SimplePOS(root)
    root.mainloop()

if __name__ == "__main__":
    main()
