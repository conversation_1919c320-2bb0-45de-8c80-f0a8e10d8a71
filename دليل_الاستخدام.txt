===============================================
نظام نقطة البيع - دليل الاستخدام السريع
===============================================

🚀 كيفية تشغيل النظام:
------------------------
1. تشغيل مباشر (مع تثبيت المتطلبات تلقائياً):
   python run.py

2. تشغيل عادي:
   python main.py

3. اختبار النظام:
   python test_system.py

📦 المتطلبات:
--------------
- Python 3.6 أو أحدث
- المكتبات: reportlab, qrcode[pil], Pillow
- (tkinter و sqlite3 مدمجان مع Python)

🛍️ كيفية الاستخدام:
--------------------

1️⃣ إضافة منتجات جديدة:
   - انقر "إضافة منتج"
   - أدخل: الاسم، السعر، الكمية، الباركود، الفئة
   - انقر "حفظ"

2️⃣ البحث عن المنتجات:
   - اكتب في مربع البحث
   - البحث يتم بالاسم أو الباركود
   - النتائج تظهر فوراً

3️⃣ إضافة منتجات للسلة:
   - انقر نقراً مزدوجاً على المنتج
   - أو حدد المنتج واضغط Enter
   - المنتج يُضاف للسلة تلقائياً

4️⃣ إدارة السلة:
   - حذف منتج: حدد المنتج في السلة واضغط "حذف من السلة"
   - مسح السلة: اضغط "مسح السلة"
   - المجاميع تُحسب تلقائياً (مع ضريبة 15%)

5️⃣ إنشاء الفاتورة:
   - أدخل اسم العميل (اختياري)
   - أدخل الرقم الضريبي للعميل (اختياري)
   - اضغط "إنشاء فاتورة"
   - سيتم إنتاج ملف PDF مع QR Code

6️⃣ طباعة الفاتورة:
   - اضغط "طباعة آخر فاتورة"
   - سيفتح ملف PDF للطباعة

📊 البيانات الافتراضية:
-------------------------
النظام يأتي مع:
- شركة المثال التجارية (يمكن تعديلها في قاعدة البيانات)
- 5 منتجات تجريبية
- إعدادات افتراضية

🔧 الملفات المهمة:
-------------------
- main.py: الواجهة الرئيسية
- database.py: إدارة قاعدة البيانات
- invoice_generator.py: إنتاج الفواتير
- pos_system.db: قاعدة البيانات (تُنشأ تلقائياً)
- الفواتير: تُحفظ كملفات PDF في نفس المجلد

📋 معايير QR Code:
-------------------
QR Code في الفواتير يحتوي على:
✓ اسم البائع
✓ الرقم الضريبي
✓ وقت إصدار الفاتورة
✓ المبلغ الإجمالي
✓ مبلغ ضريبة القيمة المضافة

حسب معايير هيئة الزكاة والضريبة والجمارك السعودية

🛠️ حل المشاكل الشائعة:
-------------------------

❌ خطأ في استيراد المكتبات:
   pip install reportlab qrcode[pil] Pillow

❌ لا يفتح النظام:
   - تأكد من Python 3.6+
   - شغل: python test_system.py

❌ لا تُنشأ الفواتير:
   - تأكد من صلاحيات الكتابة في المجلد
   - تأكد من وجود مساحة كافية

❌ مشاكل في QR Code:
   - تأكد من تثبيت qrcode[pil]
   - تأكد من تثبيت Pillow

📞 نصائح للاستخدام:
--------------------
✅ احفظ نسخة احتياطية من pos_system.db
✅ لا تحذف الملفات الأساسية
✅ يمكن تعديل إعدادات الشركة في قاعدة البيانات
✅ الفواتير تُحفظ تلقائياً كملفات PDF
✅ يمكن طباعة الفواتير من أي برنامج PDF

===============================================
تم تطوير النظام بلغة Python
مع دعم كامل للغة العربية ومعايير ZATCA
===============================================
