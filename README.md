# نظام نقطة البيع - POS System

نظام نقطة بيع بسيط مطور بلغة Python يحتوي على قاعدة بيانات SQLite وإنتاج فواتير PDF مع QR Code حسب معايير هيئة الزكاة والضريبة والجمارك السعودية.

## المميزات

- ✅ واجهة مستخدم بسيطة وسهلة الاستخدام
- ✅ قاعدة بيانات SQLite لحفظ المنتجات والفواتير
- ✅ إدارة المخزون والكميات
- ✅ البحث في المنتجات بالاسم أو الباركود
- ✅ سلة تسوق تفاعلية
- ✅ حساب ضريبة القيمة المضافة (15%)
- ✅ إنتاج فواتير PDF احترافية
- ✅ QR Code حسب معايير هيئة الزكاة السعودية
- ✅ طباعة الفواتير

## متطلبات النظام

- Python 3.6 أو أحدث
- نظام التشغيل: Windows, macOS, أو Linux

## المكتبات المطلوبة

```
tkinter (مدمجة مع Python)
sqlite3 (مدمجة مع Python)
reportlab
qrcode[pil]
Pillow
```

## التثبيت والتشغيل

### الطريقة الأولى: التشغيل المباشر
```bash
python run.py
```

### الطريقة الثانية: التثبيت اليدوي
```bash
# تثبيت المتطلبات
pip install reportlab qrcode[pil] Pillow

# تشغيل النظام
python main.py
```

## كيفية الاستخدام

### 1. إضافة المنتجات
- انقر على زر "إضافة منتج" في الواجهة الرئيسية
- أدخل بيانات المنتج (الاسم، السعر، الكمية، الباركود، الفئة)
- انقر "حفظ" لإضافة المنتج

### 2. البحث عن المنتجات
- استخدم مربع البحث للبحث بالاسم أو الباركود
- النتائج ستظهر فوراً في جدول المنتجات

### 3. إضافة المنتجات للسلة
- انقر نقراً مزدوجاً على المنتج في جدول المنتجات
- أو حدد المنتج واضغط Enter
- المنتج سيُضاف إلى سلة التسوق

### 4. إدارة السلة
- يمكن حذف منتجات من السلة
- يمكن مسح السلة بالكامل
- يتم حساب المجاميع تلقائياً

### 5. إنشاء الفاتورة
- أدخل اسم العميل والرقم الضريبي (اختياري)
- انقر "إنشاء فاتورة"
- سيتم إنتاج ملف PDF للفاتورة مع QR Code

## هيكل قاعدة البيانات

### جدول المنتجات (products)
- id: الرقم التسلسلي
- name: اسم المنتج
- price: السعر
- quantity: الكمية المتوفرة
- barcode: الباركود
- category: الفئة
- created_at: تاريخ الإضافة

### جدول الفواتير (invoices)
- id: رقم الفاتورة
- invoice_number: رقم الفاتورة النصي
- customer_name: اسم العميل
- customer_vat: الرقم الضريبي للعميل
- total_amount: المبلغ الإجمالي
- vat_amount: مبلغ الضريبة
- created_at: تاريخ الإنشاء

### جدول عناصر الفاتورة (invoice_items)
- id: الرقم التسلسلي
- invoice_id: رقم الفاتورة
- product_id: رقم المنتج
- product_name: اسم المنتج
- quantity: الكمية
- unit_price: سعر الوحدة
- total_price: السعر الإجمالي

### جدول إعدادات الشركة (company_settings)
- company_name: اسم الشركة
- vat_number: الرقم الضريبي
- address: العنوان
- phone: الهاتف
- email: البريد الإلكتروني

## QR Code حسب معايير ZATCA

يتم إنتاج QR Code في الفواتير حسب المعايير المطلوبة من هيئة الزكاة والضريبة والجمارك، ويحتوي على:

1. اسم البائع
2. الرقم الضريبي
3. وقت إصدار الفاتورة
4. المبلغ الإجمالي
5. مبلغ ضريبة القيمة المضافة

## البيانات الافتراضية

يحتوي النظام على بيانات تجريبية افتراضية:
- شركة المثال التجارية
- 5 منتجات تجريبية
- إعدادات افتراضية للشركة

## الملفات المهمة

- `main.py`: الملف الرئيسي للواجهة
- `database.py`: إدارة قاعدة البيانات
- `invoice_generator.py`: إنتاج الفواتير والـ QR Code
- `run.py`: ملف التشغيل مع تثبيت المتطلبات
- `pos_system.db`: ملف قاعدة البيانات (يتم إنشاؤه تلقائياً)

## الدعم والمساعدة

في حالة وجود مشاكل:
1. تأكد من تثبيت Python 3.6 أو أحدث
2. تأكد من تثبيت جميع المتطلبات
3. تحقق من وجود صلاحيات الكتابة في مجلد التطبيق

## الترخيص

هذا المشروع مفتوح المصدر ويمكن استخدامه وتعديله بحرية.

---

**ملاحظة**: هذا النظام مطور لأغراض تعليمية وتجريبية. للاستخدام التجاري، يُنصح بإجراء اختبارات شاملة والتأكد من التوافق مع جميع المتطلبات القانونية.
